([('ArchiveSystem.exe',
   'C:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\build\\archive_system\\ArchiveSystem.exe',
   'EXECUTABLE'),
  ('python313.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python313.dll',
   'BINARY'),
  ('unicodedata.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('select.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_lzma.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_bz2.pyd',
   'EXTENSION'),
  ('_multiprocessing.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_ssl.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_ssl.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_queue.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_queue.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('_wmi.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_wmi.pyd',
   'EXTENSION'),
  ('sqlalchemy\\cyextension\\resultproxy.cp313-win_amd64.pyd',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\cyextension\\resultproxy.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sqlalchemy\\cyextension\\util.cp313-win_amd64.pyd',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\cyextension\\util.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sqlalchemy\\cyextension\\processors.cp313-win_amd64.pyd',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\cyextension\\processors.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sqlalchemy\\cyextension\\immutabledict.cp313-win_amd64.pyd',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\cyextension\\immutabledict.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sqlalchemy\\cyextension\\collections.cp313-win_amd64.pyd',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\cyextension\\collections.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('_uuid.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_uuid.pyd',
   'EXTENSION'),
  ('greenlet\\_greenlet.cp313-win_amd64.pyd',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\greenlet\\_greenlet.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('_sqlite3.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_sqlite3.pyd',
   'EXTENSION'),
  ('PIL\\_webp.cp313-win_amd64.pyd',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\_webp.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingtk.cp313-win_amd64.pyd',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\_imagingtk.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingmorph.cp313-win_amd64.pyd',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\_imagingmorph.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingmath.cp313-win_amd64.pyd',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\_imagingmath.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingft.cp313-win_amd64.pyd',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\_imagingft.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingcms.cp313-win_amd64.pyd',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\_imagingcms.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imaging.cp313-win_amd64.pyd',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\_imaging.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('_elementtree.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_elementtree.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp313-win_amd64.pyd',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\charset_normalizer\\md__mypyc.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp313-win_amd64.pyd',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\charset_normalizer\\md.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('markupsafe\\_speedups.cp313-win_amd64.pyd',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\markupsafe\\_speedups.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('VCRUNTIME140.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\VCRUNTIME140.dll',
   'BINARY'),
  ('libcrypto-3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\libcrypto-3.dll',
   'BINARY'),
  ('libssl-3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\libssl-3.dll',
   'BINARY'),
  ('libffi-8.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\libffi-8.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('sqlite3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\sqlite3.dll',
   'BINARY'),
  ('backups\\backup_20250625_013640.db',
   'C:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\backups\\backup_20250625_013640.db',
   'DATA'),
  ('flask\\py.typed',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\flask\\py.typed',
   'DATA'),
  ('flask\\sansio\\README.md',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\flask\\sansio\\README.md',
   'DATA'),
  ('flask_sqlalchemy\\py.typed',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\flask_sqlalchemy\\py.typed',
   'DATA'),
  ('instance\\archive.db',
   'C:\\Users\\<USER>\\OneDrive\\Desktop\\test 111\\safaa\\instance\\archive.db',
   'DATA'),
  ('reportlab\\fonts\\00readme.txt',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\fonts\\00readme.txt',
   'DATA'),
  ('reportlab\\fonts\\DarkGarden-changelog.txt',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\fonts\\DarkGarden-changelog.txt',
   'DATA'),
  ('reportlab\\fonts\\DarkGarden-copying-gpl.txt',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\fonts\\DarkGarden-copying-gpl.txt',
   'DATA'),
  ('reportlab\\fonts\\DarkGarden-copying.txt',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\fonts\\DarkGarden-copying.txt',
   'DATA'),
  ('reportlab\\fonts\\DarkGarden-readme.txt',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\fonts\\DarkGarden-readme.txt',
   'DATA'),
  ('reportlab\\fonts\\DarkGarden.sfd',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\fonts\\DarkGarden.sfd',
   'DATA'),
  ('reportlab\\fonts\\DarkGardenMK.afm',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\fonts\\DarkGardenMK.afm',
   'DATA'),
  ('reportlab\\fonts\\DarkGardenMK.pfb',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\fonts\\DarkGardenMK.pfb',
   'DATA'),
  ('reportlab\\fonts\\Vera.ttf',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\fonts\\Vera.ttf',
   'DATA'),
  ('reportlab\\fonts\\VeraBI.ttf',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\fonts\\VeraBI.ttf',
   'DATA'),
  ('reportlab\\fonts\\VeraBd.ttf',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\fonts\\VeraBd.ttf',
   'DATA'),
  ('reportlab\\fonts\\VeraIt.ttf',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\fonts\\VeraIt.ttf',
   'DATA'),
  ('reportlab\\fonts\\_a______.pfb',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\fonts\\_a______.pfb',
   'DATA'),
  ('reportlab\\fonts\\_ab_____.pfb',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\fonts\\_ab_____.pfb',
   'DATA'),
  ('reportlab\\fonts\\_abi____.pfb',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\fonts\\_abi____.pfb',
   'DATA'),
  ('reportlab\\fonts\\_ai_____.pfb',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\fonts\\_ai_____.pfb',
   'DATA'),
  ('reportlab\\fonts\\_eb_____.pfb',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\fonts\\_eb_____.pfb',
   'DATA'),
  ('reportlab\\fonts\\_ebi____.pfb',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\fonts\\_ebi____.pfb',
   'DATA'),
  ('reportlab\\fonts\\_ei_____.pfb',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\fonts\\_ei_____.pfb',
   'DATA'),
  ('reportlab\\fonts\\_er_____.pfb',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\fonts\\_er_____.pfb',
   'DATA'),
  ('reportlab\\fonts\\bitstream-vera-license.txt',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\fonts\\bitstream-vera-license.txt',
   'DATA'),
  ('reportlab\\fonts\\callig15.afm',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\fonts\\callig15.afm',
   'DATA'),
  ('reportlab\\fonts\\callig15.pfb',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\fonts\\callig15.pfb',
   'DATA'),
  ('reportlab\\fonts\\cob_____.pfb',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\fonts\\cob_____.pfb',
   'DATA'),
  ('reportlab\\fonts\\cobo____.pfb',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\fonts\\cobo____.pfb',
   'DATA'),
  ('reportlab\\fonts\\com_____.pfb',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\fonts\\com_____.pfb',
   'DATA'),
  ('reportlab\\fonts\\coo_____.pfb',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\fonts\\coo_____.pfb',
   'DATA'),
  ('reportlab\\fonts\\hb-test.ttf',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\fonts\\hb-test.ttf',
   'DATA'),
  ('reportlab\\fonts\\sy______.pfb',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\fonts\\sy______.pfb',
   'DATA'),
  ('reportlab\\fonts\\zd______.pfb',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\fonts\\zd______.pfb',
   'DATA'),
  ('reportlab\\fonts\\zx______.pfb',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\fonts\\zx______.pfb',
   'DATA'),
  ('reportlab\\fonts\\zy______.pfb',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\fonts\\zy______.pfb',
   'DATA'),
  ('templates\\auth\\login.html',
   'C:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\templates\\auth\\login.html',
   'DATA'),
  ('templates\\base.html',
   'C:\\Users\\<USER>\\OneDrive\\Desktop\\test 111\\safaa\\templates\\base.html',
   'DATA'),
  ('templates\\dashboard.html',
   'C:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\templates\\dashboard.html',
   'DATA'),
  ('templates\\documents\\add.html',
   'C:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\templates\\documents\\add.html',
   'DATA'),
  ('templates\\documents\\edit.html',
   'C:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\templates\\documents\\edit.html',
   'DATA'),
  ('templates\\documents\\list.html',
   'C:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\templates\\documents\\list.html',
   'DATA'),
  ('templates\\documents\\view.html',
   'C:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\templates\\documents\\view.html',
   'DATA'),
  ('templates\\reports\\index.html',
   'C:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\templates\\reports\\index.html',
   'DATA'),
  ('templates\\reports\\result.html',
   'C:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\templates\\reports\\result.html',
   'DATA'),
  ('templates\\search.html',
   'C:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\templates\\search.html',
   'DATA'),
  ('templates\\settings\\index.html',
   'C:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\templates\\settings\\index.html',
   'DATA'),
  ('templates\\users\\add.html',
   'C:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\templates\\users\\add.html',
   'DATA'),
  ('templates\\users\\edit.html',
   'C:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\templates\\users\\edit.html',
   'DATA'),
  ('templates\\users\\list.html',
   'C:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\templates\\users\\list.html',
   'DATA'),
  ('uploads\\20250624_234827_png',
   'C:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\uploads\\20250624_234827_png',
   'DATA'),
  ('uploads\\20250624_235119_png',
   'C:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\uploads\\20250624_235119_png',
   'DATA'),
  ('wtforms\\locale\\README.md',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\wtforms\\locale\\README.md',
   'DATA'),
  ('wtforms\\locale\\ar\\LC_MESSAGES\\wtforms.mo',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\wtforms\\locale\\ar\\LC_MESSAGES\\wtforms.mo',
   'DATA'),
  ('wtforms\\locale\\ar\\LC_MESSAGES\\wtforms.po',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\wtforms\\locale\\ar\\LC_MESSAGES\\wtforms.po',
   'DATA'),
  ('wtforms\\locale\\bg\\LC_MESSAGES\\wtforms.mo',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\wtforms\\locale\\bg\\LC_MESSAGES\\wtforms.mo',
   'DATA'),
  ('wtforms\\locale\\bg\\LC_MESSAGES\\wtforms.po',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\wtforms\\locale\\bg\\LC_MESSAGES\\wtforms.po',
   'DATA'),
  ('wtforms\\locale\\ca\\LC_MESSAGES\\wtforms.mo',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\wtforms\\locale\\ca\\LC_MESSAGES\\wtforms.mo',
   'DATA'),
  ('wtforms\\locale\\ca\\LC_MESSAGES\\wtforms.po',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\wtforms\\locale\\ca\\LC_MESSAGES\\wtforms.po',
   'DATA'),
  ('wtforms\\locale\\cs_CZ\\LC_MESSAGES\\wtforms.mo',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\wtforms\\locale\\cs_CZ\\LC_MESSAGES\\wtforms.mo',
   'DATA'),
  ('wtforms\\locale\\cs_CZ\\LC_MESSAGES\\wtforms.po',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\wtforms\\locale\\cs_CZ\\LC_MESSAGES\\wtforms.po',
   'DATA'),
  ('wtforms\\locale\\cy\\LC_MESSAGES\\wtforms.mo',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\wtforms\\locale\\cy\\LC_MESSAGES\\wtforms.mo',
   'DATA'),
  ('wtforms\\locale\\cy\\LC_MESSAGES\\wtforms.po',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\wtforms\\locale\\cy\\LC_MESSAGES\\wtforms.po',
   'DATA'),
  ('wtforms\\locale\\de\\LC_MESSAGES\\wtforms.mo',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\wtforms\\locale\\de\\LC_MESSAGES\\wtforms.mo',
   'DATA'),
  ('wtforms\\locale\\de\\LC_MESSAGES\\wtforms.po',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\wtforms\\locale\\de\\LC_MESSAGES\\wtforms.po',
   'DATA'),
  ('wtforms\\locale\\de_CH\\LC_MESSAGES\\wtforms.mo',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\wtforms\\locale\\de_CH\\LC_MESSAGES\\wtforms.mo',
   'DATA'),
  ('wtforms\\locale\\de_CH\\LC_MESSAGES\\wtforms.po',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\wtforms\\locale\\de_CH\\LC_MESSAGES\\wtforms.po',
   'DATA'),
  ('wtforms\\locale\\el\\LC_MESSAGES\\wtforms.mo',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\wtforms\\locale\\el\\LC_MESSAGES\\wtforms.mo',
   'DATA'),
  ('wtforms\\locale\\el\\LC_MESSAGES\\wtforms.po',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\wtforms\\locale\\el\\LC_MESSAGES\\wtforms.po',
   'DATA'),
  ('wtforms\\locale\\en\\LC_MESSAGES\\wtforms.mo',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\wtforms\\locale\\en\\LC_MESSAGES\\wtforms.mo',
   'DATA'),
  ('wtforms\\locale\\en\\LC_MESSAGES\\wtforms.po',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\wtforms\\locale\\en\\LC_MESSAGES\\wtforms.po',
   'DATA'),
  ('wtforms\\locale\\es\\LC_MESSAGES\\wtforms.mo',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\wtforms\\locale\\es\\LC_MESSAGES\\wtforms.mo',
   'DATA'),
  ('wtforms\\locale\\es\\LC_MESSAGES\\wtforms.po',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\wtforms\\locale\\es\\LC_MESSAGES\\wtforms.po',
   'DATA'),
  ('wtforms\\locale\\et\\LC_MESSAGES\\wtforms.mo',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\wtforms\\locale\\et\\LC_MESSAGES\\wtforms.mo',
   'DATA'),
  ('wtforms\\locale\\et\\LC_MESSAGES\\wtforms.po',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\wtforms\\locale\\et\\LC_MESSAGES\\wtforms.po',
   'DATA'),
  ('wtforms\\locale\\fa\\LC_MESSAGES\\wtforms.mo',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\wtforms\\locale\\fa\\LC_MESSAGES\\wtforms.mo',
   'DATA'),
  ('wtforms\\locale\\fa\\LC_MESSAGES\\wtforms.po',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\wtforms\\locale\\fa\\LC_MESSAGES\\wtforms.po',
   'DATA'),
  ('wtforms\\locale\\fi\\LC_MESSAGES\\wtforms.mo',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\wtforms\\locale\\fi\\LC_MESSAGES\\wtforms.mo',
   'DATA'),
  ('wtforms\\locale\\fi\\LC_MESSAGES\\wtforms.po',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\wtforms\\locale\\fi\\LC_MESSAGES\\wtforms.po',
   'DATA'),
  ('wtforms\\locale\\fr\\LC_MESSAGES\\wtforms.mo',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\wtforms\\locale\\fr\\LC_MESSAGES\\wtforms.mo',
   'DATA'),
  ('wtforms\\locale\\fr\\LC_MESSAGES\\wtforms.po',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\wtforms\\locale\\fr\\LC_MESSAGES\\wtforms.po',
   'DATA'),
  ('wtforms\\locale\\he\\LC_MESSAGES\\wtforms.mo',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\wtforms\\locale\\he\\LC_MESSAGES\\wtforms.mo',
   'DATA'),
  ('wtforms\\locale\\he\\LC_MESSAGES\\wtforms.po',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\wtforms\\locale\\he\\LC_MESSAGES\\wtforms.po',
   'DATA'),
  ('wtforms\\locale\\hu\\LC_MESSAGES\\wtforms.mo',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\wtforms\\locale\\hu\\LC_MESSAGES\\wtforms.mo',
   'DATA'),
  ('wtforms\\locale\\hu\\LC_MESSAGES\\wtforms.po',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\wtforms\\locale\\hu\\LC_MESSAGES\\wtforms.po',
   'DATA'),
  ('wtforms\\locale\\it\\LC_MESSAGES\\wtforms.mo',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\wtforms\\locale\\it\\LC_MESSAGES\\wtforms.mo',
   'DATA'),
  ('wtforms\\locale\\it\\LC_MESSAGES\\wtforms.po',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\wtforms\\locale\\it\\LC_MESSAGES\\wtforms.po',
   'DATA'),
  ('wtforms\\locale\\ja\\LC_MESSAGES\\wtforms.mo',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\wtforms\\locale\\ja\\LC_MESSAGES\\wtforms.mo',
   'DATA'),
  ('wtforms\\locale\\ja\\LC_MESSAGES\\wtforms.po',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\wtforms\\locale\\ja\\LC_MESSAGES\\wtforms.po',
   'DATA'),
  ('wtforms\\locale\\kk\\LC_MESSAGES\\wtforms.mo',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\wtforms\\locale\\kk\\LC_MESSAGES\\wtforms.mo',
   'DATA'),
  ('wtforms\\locale\\kk\\LC_MESSAGES\\wtforms.po',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\wtforms\\locale\\kk\\LC_MESSAGES\\wtforms.po',
   'DATA'),
  ('wtforms\\locale\\ko\\LC_MESSAGES\\wtforms.mo',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\wtforms\\locale\\ko\\LC_MESSAGES\\wtforms.mo',
   'DATA'),
  ('wtforms\\locale\\ko\\LC_MESSAGES\\wtforms.po',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\wtforms\\locale\\ko\\LC_MESSAGES\\wtforms.po',
   'DATA'),
  ('wtforms\\locale\\nb\\LC_MESSAGES\\wtforms.mo',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\wtforms\\locale\\nb\\LC_MESSAGES\\wtforms.mo',
   'DATA'),
  ('wtforms\\locale\\nb\\LC_MESSAGES\\wtforms.po',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\wtforms\\locale\\nb\\LC_MESSAGES\\wtforms.po',
   'DATA'),
  ('wtforms\\locale\\nl\\LC_MESSAGES\\wtforms.mo',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\wtforms\\locale\\nl\\LC_MESSAGES\\wtforms.mo',
   'DATA'),
  ('wtforms\\locale\\nl\\LC_MESSAGES\\wtforms.po',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\wtforms\\locale\\nl\\LC_MESSAGES\\wtforms.po',
   'DATA'),
  ('wtforms\\locale\\pl\\LC_MESSAGES\\wtforms.mo',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\wtforms\\locale\\pl\\LC_MESSAGES\\wtforms.mo',
   'DATA'),
  ('wtforms\\locale\\pl\\LC_MESSAGES\\wtforms.po',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\wtforms\\locale\\pl\\LC_MESSAGES\\wtforms.po',
   'DATA'),
  ('wtforms\\locale\\pt\\LC_MESSAGES\\wtforms.mo',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\wtforms\\locale\\pt\\LC_MESSAGES\\wtforms.mo',
   'DATA'),
  ('wtforms\\locale\\pt\\LC_MESSAGES\\wtforms.po',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\wtforms\\locale\\pt\\LC_MESSAGES\\wtforms.po',
   'DATA'),
  ('wtforms\\locale\\ro\\LC_MESSAGES\\wtforms.mo',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\wtforms\\locale\\ro\\LC_MESSAGES\\wtforms.mo',
   'DATA'),
  ('wtforms\\locale\\ro\\LC_MESSAGES\\wtforms.po',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\wtforms\\locale\\ro\\LC_MESSAGES\\wtforms.po',
   'DATA'),
  ('wtforms\\locale\\ru\\LC_MESSAGES\\wtforms.mo',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\wtforms\\locale\\ru\\LC_MESSAGES\\wtforms.mo',
   'DATA'),
  ('wtforms\\locale\\ru\\LC_MESSAGES\\wtforms.po',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\wtforms\\locale\\ru\\LC_MESSAGES\\wtforms.po',
   'DATA'),
  ('wtforms\\locale\\sk\\LC_MESSAGES\\wtforms.mo',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\wtforms\\locale\\sk\\LC_MESSAGES\\wtforms.mo',
   'DATA'),
  ('wtforms\\locale\\sk\\LC_MESSAGES\\wtforms.po',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\wtforms\\locale\\sk\\LC_MESSAGES\\wtforms.po',
   'DATA'),
  ('wtforms\\locale\\sv\\LC_MESSAGES\\wtforms.mo',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\wtforms\\locale\\sv\\LC_MESSAGES\\wtforms.mo',
   'DATA'),
  ('wtforms\\locale\\sv\\LC_MESSAGES\\wtforms.po',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\wtforms\\locale\\sv\\LC_MESSAGES\\wtforms.po',
   'DATA'),
  ('wtforms\\locale\\tr\\LC_MESSAGES\\wtforms.mo',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\wtforms\\locale\\tr\\LC_MESSAGES\\wtforms.mo',
   'DATA'),
  ('wtforms\\locale\\tr\\LC_MESSAGES\\wtforms.po',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\wtforms\\locale\\tr\\LC_MESSAGES\\wtforms.po',
   'DATA'),
  ('wtforms\\locale\\uk\\LC_MESSAGES\\wtforms.mo',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\wtforms\\locale\\uk\\LC_MESSAGES\\wtforms.mo',
   'DATA'),
  ('wtforms\\locale\\uk\\LC_MESSAGES\\wtforms.po',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\wtforms\\locale\\uk\\LC_MESSAGES\\wtforms.po',
   'DATA'),
  ('wtforms\\locale\\wtforms.pot',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\wtforms\\locale\\wtforms.pot',
   'DATA'),
  ('wtforms\\locale\\zh\\LC_MESSAGES\\wtforms.mo',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\wtforms\\locale\\zh\\LC_MESSAGES\\wtforms.mo',
   'DATA'),
  ('wtforms\\locale\\zh\\LC_MESSAGES\\wtforms.po',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\wtforms\\locale\\zh\\LC_MESSAGES\\wtforms.po',
   'DATA'),
  ('wtforms\\locale\\zh_TW\\LC_MESSAGES\\wtforms.mo',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\wtforms\\locale\\zh_TW\\LC_MESSAGES\\wtforms.mo',
   'DATA'),
  ('wtforms\\locale\\zh_TW\\LC_MESSAGES\\wtforms.po',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\wtforms\\locale\\zh_TW\\LC_MESSAGES\\wtforms.po',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'DATA'),
  ('setuptools\\_vendor\\jaraco\\text\\Lorem ipsum.txt',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\Lorem '
   'ipsum.txt',
   'DATA'),
  ('flask_sqlalchemy-3.1.1.dist-info\\WHEEL',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\flask_sqlalchemy-3.1.1.dist-info\\WHEEL',
   'DATA'),
  ('flask_sqlalchemy-3.1.1.dist-info\\METADATA',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\flask_sqlalchemy-3.1.1.dist-info\\METADATA',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\LICENSE.txt',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\werkzeug-3.1.3.dist-info\\LICENSE.txt',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\RECORD',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\itsdangerous-2.2.0.dist-info\\RECORD',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\METADATA',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\METADATA',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\INSTALLER',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\INSTALLER',
   'DATA'),
  ('click-8.2.1.dist-info\\INSTALLER',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\click-8.2.1.dist-info\\INSTALLER',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\METADATA',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\werkzeug-3.1.3.dist-info\\METADATA',
   'DATA'),
  ('click-8.2.1.dist-info\\METADATA',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\click-8.2.1.dist-info\\METADATA',
   'DATA'),
  ('flask-3.1.1.dist-info\\METADATA',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\flask-3.1.1.dist-info\\METADATA',
   'DATA'),
  ('click-8.2.1.dist-info\\RECORD',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\click-8.2.1.dist-info\\RECORD',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\RECORD',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\werkzeug-3.1.3.dist-info\\RECORD',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\INSTALLER',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\werkzeug-3.1.3.dist-info\\INSTALLER',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\LICENSE.txt',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\LICENSE.txt',
   'DATA'),
  ('flask-3.1.1.dist-info\\RECORD',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\flask-3.1.1.dist-info\\RECORD',
   'DATA'),
  ('click-8.2.1.dist-info\\WHEEL',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\click-8.2.1.dist-info\\WHEEL',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\INSTALLER',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\itsdangerous-2.2.0.dist-info\\INSTALLER',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\LICENSE.txt',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\itsdangerous-2.2.0.dist-info\\LICENSE.txt',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\METADATA',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\itsdangerous-2.2.0.dist-info\\METADATA',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\WHEEL',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\itsdangerous-2.2.0.dist-info\\WHEEL',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\WHEEL',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\werkzeug-3.1.3.dist-info\\WHEEL',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\top_level.txt',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\top_level.txt',
   'DATA'),
  ('flask-3.1.1.dist-info\\licenses\\LICENSE.txt',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\flask-3.1.1.dist-info\\licenses\\LICENSE.txt',
   'DATA'),
  ('click-8.2.1.dist-info\\licenses\\LICENSE.txt',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\click-8.2.1.dist-info\\licenses\\LICENSE.txt',
   'DATA'),
  ('flask-3.1.1.dist-info\\INSTALLER',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\flask-3.1.1.dist-info\\INSTALLER',
   'DATA'),
  ('flask-3.1.1.dist-info\\REQUESTED',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\flask-3.1.1.dist-info\\REQUESTED',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\RECORD',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\RECORD',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\WHEEL',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\WHEEL',
   'DATA'),
  ('flask-3.1.1.dist-info\\entry_points.txt',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\flask-3.1.1.dist-info\\entry_points.txt',
   'DATA'),
  ('flask_sqlalchemy-3.1.1.dist-info\\INSTALLER',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\flask_sqlalchemy-3.1.1.dist-info\\INSTALLER',
   'DATA'),
  ('flask-3.1.1.dist-info\\WHEEL',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\flask-3.1.1.dist-info\\WHEEL',
   'DATA'),
  ('flask_sqlalchemy-3.1.1.dist-info\\RECORD',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\flask_sqlalchemy-3.1.1.dist-info\\RECORD',
   'DATA'),
  ('flask_sqlalchemy-3.1.1.dist-info\\REQUESTED',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\flask_sqlalchemy-3.1.1.dist-info\\REQUESTED',
   'DATA'),
  ('flask_sqlalchemy-3.1.1.dist-info\\LICENSE.rst',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\flask_sqlalchemy-3.1.1.dist-info\\LICENSE.rst',
   'DATA'),
  ('base_library.zip',
   'C:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\build\\archive_system\\base_library.zip',
   'DATA')],)
