"""
ملف إنشاء البيانات الأولية للنظام
"""

from app import app
from models import db, User, Department, DocumentType, SystemSettings
from datetime import datetime

def init_database():
    """إنشاء قاعدة البيانات والبيانات الأولية"""
    
    with app.app_context():
        # إنشاء الجداول
        db.create_all()
        
        # إنشاء المستخدم الافتراضي
        if not User.query.filter_by(username='admin').first():
            admin_user = User(
                username='admin',
                email='<EMAIL>',
                full_name='مدير النظام',
                role='admin',
                department='إدارة النظام',
                is_active=True
            )
            admin_user.set_password('admin123')
            db.session.add(admin_user)
            print("✓ تم إنشاء المستخدم الافتراضي: admin / admin123")
        
        # إنشاء مستخدم مدير
        if not User.query.filter_by(username='manager').first():
            manager_user = User(
                username='manager',
                email='<EMAIL>',
                full_name='مدير الأرشيف',
                role='manager',
                department='قسم الأرشيف',
                is_active=True
            )
            manager_user.set_password('manager123')
            db.session.add(manager_user)
            print("✓ تم إنشاء مستخدم المدير: manager / manager123")
        
        # إنشاء مستخدم عادي
        if not User.query.filter_by(username='user').first():
            normal_user = User(
                username='user',
                email='<EMAIL>',
                full_name='موظف الأرشيف',
                role='user',
                department='قسم الكتب الواردة',
                is_active=True
            )
            normal_user.set_password('user123')
            db.session.add(normal_user)
            print("✓ تم إنشاء المستخدم العادي: user / user123")
        
        # إنشاء الأقسام الافتراضية
        departments_data = [
            {'name': 'قسم الكتب الواردة', 'code': 'INC', 'description': 'قسم استقبال ومعالجة الكتب الواردة'},
            {'name': 'قسم الكتب الصادرة', 'code': 'OUT', 'description': 'قسم إعداد وإرسال الكتب الصادرة'},
            {'name': 'قسم الأرشيف', 'code': 'ARC', 'description': 'قسم حفظ وأرشفة الكتب'},
            {'name': 'قسم المتابعة', 'code': 'FOL', 'description': 'قسم متابعة الكتب والمراسلات'},
            {'name': 'الإدارة العامة', 'code': 'GEN', 'description': 'الإدارة العامة للمؤسسة'},
            {'name': 'قسم الموارد البشرية', 'code': 'HR', 'description': 'قسم إدارة الموارد البشرية'},
            {'name': 'قسم المالية', 'code': 'FIN', 'description': 'قسم الشؤون المالية والمحاسبة'},
            {'name': 'قسم تقنية المعلومات', 'code': 'IT', 'description': 'قسم تقنية المعلومات والدعم التقني'}
        ]
        
        for dept_data in departments_data:
            if not Department.query.filter_by(code=dept_data['code']).first():
                department = Department(**dept_data)
                db.session.add(department)
        
        print("✓ تم إنشاء الأقسام الافتراضية")
        
        # إنشاء أنواع الكتب الافتراضية
        document_types_data = [
            {'name': 'كتاب رسمي', 'code': 'OFF', 'description': 'الكتب الرسمية العادية'},
            {'name': 'تعميم', 'code': 'CIR', 'description': 'التعاميم والإعلانات'},
            {'name': 'قرار إداري', 'code': 'DEC', 'description': 'القرارات الإدارية'},
            {'name': 'تقرير', 'code': 'REP', 'description': 'التقارير والدراسات'},
            {'name': 'طلب', 'code': 'REQ', 'description': 'الطلبات والاستفسارات'},
            {'name': 'شكوى', 'code': 'COM', 'description': 'الشكاوى والاعتراضات'},
            {'name': 'عقد', 'code': 'CON', 'description': 'العقود والاتفاقيات'},
            {'name': 'فاتورة', 'code': 'INV', 'description': 'الفواتير والمطالبات المالية'}
        ]
        
        for type_data in document_types_data:
            if not DocumentType.query.filter_by(code=type_data['code']).first():
                doc_type = DocumentType(**type_data)
                db.session.add(doc_type)
        
        print("✓ تم إنشاء أنواع الكتب الافتراضية")
        
        # إنشاء الإعدادات الافتراضية
        settings_data = [
            {'key': 'system_name', 'value': 'نظام الأرشفة الإلكترونية', 'description': 'اسم النظام'},
            {'key': 'organization_name', 'value': 'الحكومة العراقية', 'description': 'اسم المؤسسة'},
            {'key': 'max_file_size', 'value': '16', 'description': 'الحد الأقصى لحجم الملف بالميجابايت'},
            {'key': 'documents_per_page', 'value': '20', 'description': 'عدد الكتب في الصفحة الواحدة'},
            {'key': 'enable_notifications', 'value': 'true', 'description': 'تفعيل الإشعارات'},
            {'key': 'enable_audit_log', 'value': 'true', 'description': 'تفعيل سجل العمليات'},
            {'key': 'backup_frequency', 'value': 'daily', 'description': 'تكرار النسخ الاحتياطي'},
            {'key': 'session_timeout', 'value': '60', 'description': 'مهلة انتهاء الجلسة بالدقائق'}
        ]
        
        for setting_data in settings_data:
            if not SystemSettings.query.filter_by(key=setting_data['key']).first():
                setting = SystemSettings(**setting_data)
                db.session.add(setting)
        
        print("✓ تم إنشاء الإعدادات الافتراضية")
        
        # حفظ جميع التغييرات
        db.session.commit()
        print("✓ تم حفظ جميع البيانات الأولية بنجاح")
        
        print("\n" + "="*50)
        print("تم إعداد النظام بنجاح!")
        print("="*50)
        print("المستخدمين الافتراضيين:")
        print("- المدير العام: admin / admin123")
        print("- مدير الأرشيف: manager / manager123") 
        print("- موظف عادي: user / user123")
        print("="*50)
        print("يمكنك الآن تشغيل النظام باستخدام: python app.py")
        print("="*50)

if __name__ == '__main__':
    init_database()
