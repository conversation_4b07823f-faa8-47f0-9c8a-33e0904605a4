#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت اختبار حزمة نظام الأرشفة الإلكترونية
Electronic Archive System Package Test Script

يقوم هذا السكريبت بفحص الحزمة المُجمعة والتأكد من عملها
"""

import os
import sys
import subprocess
import time
import requests
import threading
from pathlib import Path

def print_header():
    """طباعة رأس الاختبار"""
    print("=" * 70)
    print("🧪 اختبار حزمة نظام الأرشفة الإلكترونية")
    print("📋 Electronic Archive System Package Test")
    print("=" * 70)

def check_files():
    """فحص وجود الملفات المطلوبة"""
    print("\n📁 فحص الملفات...")
    
    required_files = [
        "dist/ArchiveSystem/ArchiveSystem.exe",
        "dist/ArchiveSystem/start_archive_system.bat",
        "dist/ArchiveSystem/README_INSTALLATION.md",
        "dist/ArchiveSystem/_internal"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
            print(f"❌ مفقود: {file_path}")
        else:
            print(f"✅ موجود: {file_path}")
    
    if missing_files:
        print(f"\n❌ يوجد {len(missing_files)} ملف مفقود!")
        return False
    else:
        print("\n✅ جميع الملفات موجودة!")
        return True

def check_package_size():
    """فحص حجم الحزمة"""
    print("\n📊 فحص حجم الحزمة...")
    
    try:
        # حساب حجم مجلد التوزيع
        total_size = 0
        dist_path = Path("dist/ArchiveSystem")
        
        for file_path in dist_path.rglob("*"):
            if file_path.is_file():
                total_size += file_path.stat().st_size
        
        size_mb = total_size / (1024 * 1024)
        print(f"📦 حجم الحزمة: {size_mb:.1f} MB")
        
        if size_mb > 500:
            print("⚠️  الحزمة كبيرة نسبياً (أكثر من 500 MB)")
        elif size_mb < 50:
            print("⚠️  الحزمة صغيرة نسبياً (أقل من 50 MB) - قد تكون ملفات مفقودة")
        else:
            print("✅ حجم الحزمة مناسب")
            
        return True
        
    except Exception as e:
        print(f"❌ خطأ في حساب حجم الحزمة: {e}")
        return False

def test_executable():
    """اختبار تشغيل الملف التنفيذي"""
    print("\n🚀 اختبار تشغيل الملف التنفيذي...")
    
    exe_path = "dist/ArchiveSystem/ArchiveSystem.exe"
    
    if not os.path.exists(exe_path):
        print("❌ الملف التنفيذي غير موجود!")
        return False
    
    try:
        # تشغيل الملف التنفيذي في الخلفية
        print("⏳ بدء تشغيل النظام...")
        process = subprocess.Popen(
            [exe_path],
            cwd="dist/ArchiveSystem",
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            creationflags=subprocess.CREATE_NEW_CONSOLE
        )
        
        # انتظار قليل للتأكد من بدء التشغيل
        time.sleep(10)
        
        # التحقق من أن العملية ما زالت تعمل
        if process.poll() is None:
            print("✅ النظام يعمل بنجاح!")
            
            # محاولة الاتصال بالخادم
            test_server_connection()
            
            # إيقاف العملية
            process.terminate()
            process.wait(timeout=5)
            print("✅ تم إيقاف النظام بنجاح")
            return True
        else:
            stdout, stderr = process.communicate()
            print(f"❌ النظام توقف بشكل غير متوقع!")
            print(f"خطأ: {stderr.decode('utf-8', errors='ignore')}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام: {e}")
        return False

def test_server_connection():
    """اختبار الاتصال بالخادم"""
    print("\n🌐 اختبار الاتصال بالخادم...")
    
    # محاولة الاتصال بمنافذ مختلفة
    ports = [5000, 5001, 5002, 5003, 5004]
    
    for port in ports:
        try:
            url = f"http://localhost:{port}"
            response = requests.get(url, timeout=5)
            
            if response.status_code == 200:
                print(f"✅ الخادم يعمل على المنفذ {port}")
                print(f"📄 حجم الصفحة: {len(response.content)} بايت")
                
                # التحقق من وجود عناصر مهمة في الصفحة
                if "نظام الأرشفة" in response.text or "Archive System" in response.text:
                    print("✅ الصفحة تحتوي على محتوى النظام")
                else:
                    print("⚠️  الصفحة لا تحتوي على محتوى النظام المتوقع")
                
                return True
                
        except requests.exceptions.RequestException:
            continue
    
    print("❌ لا يمكن الاتصال بالخادم على أي منفذ")
    return False

def test_zip_package():
    """اختبار حزمة ZIP"""
    print("\n📦 اختبار حزمة ZIP...")
    
    zip_file = "ArchiveSystem_Windows11_v1.0.zip"
    
    if not os.path.exists(zip_file):
        print("❌ ملف ZIP غير موجود!")
        return False
    
    try:
        import zipfile
        
        with zipfile.ZipFile(zip_file, 'r') as zip_ref:
            file_list = zip_ref.namelist()
            
            print(f"📁 عدد الملفات في ZIP: {len(file_list)}")
            
            # التحقق من وجود الملفات المهمة
            important_files = [
                "ArchiveSystem.exe",
                "start_archive_system.bat",
                "README_INSTALLATION.md"
            ]
            
            missing_in_zip = []
            for important_file in important_files:
                found = any(important_file in f for f in file_list)
                if found:
                    print(f"✅ موجود في ZIP: {important_file}")
                else:
                    missing_in_zip.append(important_file)
                    print(f"❌ مفقود في ZIP: {important_file}")
            
            if missing_in_zip:
                print(f"❌ يوجد {len(missing_in_zip)} ملف مهم مفقود في ZIP!")
                return False
            else:
                print("✅ جميع الملفات المهمة موجودة في ZIP!")
                return True
                
    except Exception as e:
        print(f"❌ خطأ في فحص ملف ZIP: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print_header()
    
    tests = [
        ("فحص الملفات", check_files),
        ("فحص حجم الحزمة", check_package_size),
        ("اختبار حزمة ZIP", test_zip_package),
        ("اختبار الملف التنفيذي", test_executable),
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"🧪 {test_name}")
        print(f"{'='*50}")
        
        try:
            if test_func():
                passed_tests += 1
                print(f"✅ {test_name}: نجح")
            else:
                print(f"❌ {test_name}: فشل")
        except Exception as e:
            print(f"❌ {test_name}: خطأ - {e}")
    
    # النتيجة النهائية
    print(f"\n{'='*70}")
    print(f"📊 نتائج الاختبار النهائية")
    print(f"{'='*70}")
    print(f"✅ نجح: {passed_tests}/{total_tests}")
    print(f"❌ فشل: {total_tests - passed_tests}/{total_tests}")
    
    if passed_tests == total_tests:
        print("\n🎉 جميع الاختبارات نجحت! الحزمة جاهزة للتوزيع!")
        return True
    else:
        print(f"\n⚠️  {total_tests - passed_tests} اختبار فشل. يرجى مراجعة المشاكل أعلاه.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
