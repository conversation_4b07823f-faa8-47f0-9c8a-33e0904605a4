{% extends "base.html" %}

{% block title %}البحث المتقدم - نظام الأرشفة الإلكترونية{% endblock %}

{% block content %}
<div class="page-header">
    <h1><i class="fas fa-search me-2"></i>البحث المتقدم</h1>
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ url_for('dashboard') }}">لوحة التحكم</a></li>
            <li class="breadcrumb-item active">البحث المتقدم</li>
        </ol>
    </nav>
</div>

<div class="row">
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-filter me-2"></i>
                    معايير البحث
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    {{ form.hidden_tag() }}
                    
                    <div class="mb-3">
                        {{ form.search_text.label(class="form-label") }}
                        {{ form.search_text(class="form-control", placeholder="ابحث في الموضوع أو المحتوى أو رقم الكتاب") }}
                        <div class="form-text">البحث في الموضوع والمحتوى ورقم الكتاب</div>
                    </div>
                    
                    <div class="mb-3">
                        {{ form.document_number.label(class="form-label") }}
                        {{ form.document_number(class="form-control", placeholder="رقم الكتاب") }}
                    </div>
                    
                    <div class="row">
                        <div class="col-6">
                            <div class="mb-3">
                                {{ form.date_from.label(class="form-label") }}
                                {{ form.date_from(class="form-control") }}
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="mb-3">
                                {{ form.date_to.label(class="form-label") }}
                                {{ form.date_to(class="form-control") }}
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        {{ form.document_type.label(class="form-label") }}
                        {{ form.document_type(class="form-select") }}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.department_id.label(class="form-label") }}
                        {{ form.department_id(class="form-select") }}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.classification.label(class="form-label") }}
                        {{ form.classification(class="form-select") }}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.priority.label(class="form-label") }}
                        {{ form.priority(class="form-select") }}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.sender.label(class="form-label") }}
                        {{ form.sender(class="form-control", placeholder="اسم الجهة المرسلة") }}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.receiver.label(class="form-label") }}
                        {{ form.receiver(class="form-control", placeholder="اسم الجهة المستقبلة") }}
                    </div>
                    
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-2"></i>
                            بحث
                        </button>
                        <button type="button" class="btn btn-outline-secondary" onclick="clearForm()">
                            <i class="fas fa-eraser me-2"></i>
                            مسح الحقول
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- نصائح البحث -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-lightbulb me-2"></i>
                    نصائح البحث
                </h6>
            </div>
            <div class="card-body">
                <small>
                    <ul class="mb-0">
                        <li>استخدم كلمات مفتاحية واضحة</li>
                        <li>يمكنك البحث بجزء من رقم الكتاب</li>
                        <li>حدد نطاق تاريخي للبحث الدقيق</li>
                        <li>استخدم عدة معايير للحصول على نتائج أفضل</li>
                        <li>البحث غير حساس لحالة الأحرف</li>
                    </ul>
                </small>
            </div>
        </div>
    </div>
    
    <div class="col-md-8">
        {% if results %}
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>
                        نتائج البحث ({{ results.total }} نتيجة)
                    </h5>
                    {% if results.total > 0 %}
                    <div class="btn-group btn-group-sm" role="group">
                        <button type="button" class="btn btn-outline-secondary" onclick="exportResults('excel')">
                            <i class="fas fa-file-excel me-1"></i>
                            Excel
                        </button>
                        <button type="button" class="btn btn-outline-secondary" onclick="exportResults('pdf')">
                            <i class="fas fa-file-pdf me-1"></i>
                            PDF
                        </button>
                        <button type="button" class="btn btn-outline-secondary" onclick="window.print()">
                            <i class="fas fa-print me-1"></i>
                            طباعة
                        </button>
                    </div>
                    {% endif %}
                </div>
            </div>
            <div class="card-body">
                {% if results.items %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>رقم الكتاب</th>
                                <th>الموضوع</th>
                                <th>النوع</th>
                                <th>التاريخ</th>
                                <th>الأولوية</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for doc in results.items %}
                            <tr>
                                <td>
                                    <a href="{{ url_for('view_document', id=doc.id) }}" class="text-decoration-none fw-bold">
                                        {{ doc.get_full_number() }}
                                    </a>
                                </td>
                                <td>
                                    <div class="text-truncate" style="max-width: 250px;" title="{{ doc.subject }}">
                                        {{ doc.subject }}
                                    </div>
                                    {% if doc.content %}
                                    <small class="text-muted">
                                        {{ doc.content[:50] }}{% if doc.content|length > 50 %}...{% endif %}
                                    </small>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if doc.document_type == 'incoming' %}
                                        <span class="badge bg-success">وارد</span>
                                    {% elif doc.document_type == 'outgoing' %}
                                        <span class="badge bg-info">صادر</span>
                                    {% else %}
                                        <span class="badge bg-warning">داخلي</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <small>{{ doc.document_date.strftime('%Y-%m-%d') }}</small>
                                </td>
                                <td>
                                    {% if doc.priority == 'urgent' %}
                                        <span class="badge bg-danger">عاجل</span>
                                    {% elif doc.priority == 'high' %}
                                        <span class="badge bg-warning">عالي</span>
                                    {% elif doc.priority == 'low' %}
                                        <span class="badge bg-secondary">منخفض</span>
                                    {% else %}
                                        <span class="badge bg-light text-dark">عادي</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <a href="{{ url_for('view_document', id=doc.id) }}" class="btn btn-outline-primary" title="عرض">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        {% if current_user.has_permission('update') %}
                                        <a href="{{ url_for('edit_document', id=doc.id) }}" class="btn btn-outline-secondary" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                {% if results.pages > 1 %}
                <nav aria-label="تصفح النتائج">
                    <ul class="pagination justify-content-center">
                        {% if results.has_prev %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('search', page=results.prev_num, **request.form) }}">السابق</a>
                        </li>
                        {% endif %}
                        
                        {% for page_num in results.iter_pages() %}
                            {% if page_num %}
                                {% if page_num != results.page %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('search', page=page_num, **request.form) }}">{{ page_num }}</a>
                                </li>
                                {% else %}
                                <li class="page-item active">
                                    <span class="page-link">{{ page_num }}</span>
                                </li>
                                {% endif %}
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">…</span>
                            </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if results.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('search', page=results.next_num, **request.form) }}">التالي</a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
                
                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-search fa-4x text-muted mb-3"></i>
                    <h4 class="text-muted">لا توجد نتائج</h4>
                    <p class="text-muted">لم يتم العثور على كتب تطابق معايير البحث المحددة.</p>
                    <button type="button" class="btn btn-outline-primary" onclick="clearForm()">
                        <i class="fas fa-eraser me-2"></i>
                        مسح معايير البحث
                    </button>
                </div>
                {% endif %}
            </div>
        </div>
        {% else %}
        <div class="card">
            <div class="card-body text-center py-5">
                <i class="fas fa-search fa-4x text-primary mb-3"></i>
                <h4>البحث في الكتب</h4>
                <p class="text-muted">استخدم النموذج على اليسار لبدء البحث في الكتب المحفوظة.</p>
                <div class="row mt-4">
                    <div class="col-md-4">
                        <div class="card border-0 bg-light">
                            <div class="card-body">
                                <i class="fas fa-file-alt fa-2x text-primary mb-2"></i>
                                <h6>البحث النصي</h6>
                                <small class="text-muted">ابحث في الموضوع والمحتوى</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card border-0 bg-light">
                            <div class="card-body">
                                <i class="fas fa-calendar fa-2x text-success mb-2"></i>
                                <h6>البحث بالتاريخ</h6>
                                <small class="text-muted">حدد نطاق زمني للبحث</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card border-0 bg-light">
                            <div class="card-body">
                                <i class="fas fa-filter fa-2x text-info mb-2"></i>
                                <h6>البحث المتقدم</h6>
                                <small class="text-muted">استخدم معايير متعددة</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function clearForm() {
    $('form')[0].reset();
    $('form').find('select').each(function() {
        $(this).val('');
    });
}

function exportResults(format) {
    // جمع معايير البحث الحالية
    var searchParams = new URLSearchParams();
    $('form').find('input, select').each(function() {
        if ($(this).val() && $(this).attr('name') !== 'csrf_token') {
            searchParams.append($(this).attr('name'), $(this).val());
        }
    });
    
    // إضافة نوع التصدير
    searchParams.append('export', format);
    
    // فتح رابط التصدير
    window.open('/export-search?' + searchParams.toString(), '_blank');
}

// حفظ آخر بحث في localStorage
$(document).ready(function() {
    // استرجاع آخر بحث
    var lastSearch = localStorage.getItem('lastSearch');
    if (lastSearch && !$('form input[name="search_text"]').val()) {
        var searchData = JSON.parse(lastSearch);
        Object.keys(searchData).forEach(function(key) {
            $('form [name="' + key + '"]').val(searchData[key]);
        });
    }
    
    // حفظ البحث الحالي
    $('form').on('submit', function() {
        var formData = {};
        $(this).find('input, select').each(function() {
            if ($(this).val() && $(this).attr('name') !== 'csrf_token') {
                formData[$(this).attr('name')] = $(this).val();
            }
        });
        localStorage.setItem('lastSearch', JSON.stringify(formData));
    });
});
</script>
{% endblock %}
