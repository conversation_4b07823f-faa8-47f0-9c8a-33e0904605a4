{% extends "base.html" %}

{% block title %}إدارة المستخدمين - نظام الأرشفة الإلكترونية{% endblock %}

{% block content %}
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1><i class="fas fa-users me-2"></i>إدارة المستخدمين</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('dashboard') }}">لوحة التحكم</a></li>
                    <li class="breadcrumb-item active">إدارة المستخدمين</li>
                </ol>
            </nav>
        </div>
        <a href="{{ url_for('add_user') }}" class="btn btn-primary">
            <i class="fas fa-user-plus me-2"></i>
            إضافة مستخدم جديد
        </a>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-list me-2"></i>
            قائمة المستخدمين ({{ users.total }} مستخدم)
        </h5>
    </div>
    <div class="card-body">
        {% if users.items %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-light">
                    <tr>
                        <th>اسم المستخدم</th>
                        <th>الاسم الكامل</th>
                        <th>القسم</th>
                        <th>الدور</th>
                        <th>الحالة</th>
                        <th>آخر دخول</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for user in users.items %}
                    <tr>
                        <td>
                            <strong>{{ user.username }}</strong>
                        </td>
                        <td>{{ user.full_name }}</td>
                        <td>{{ user.department }}</td>
                        <td>
                            {% if user.role == 'admin' %}
                                <span class="badge bg-danger">مدير النظام</span>
                            {% elif user.role == 'manager' %}
                                <span class="badge bg-warning">مدير</span>
                            {% elif user.role == 'user' %}
                                <span class="badge bg-primary">مستخدم</span>
                            {% else %}
                                <span class="badge bg-secondary">مشاهد</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if user.is_active %}
                                <span class="badge bg-success">نشط</span>
                            {% else %}
                                <span class="badge bg-secondary">غير نشط</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if user.last_login %}
                                <small>{{ user.last_login.strftime('%Y-%m-%d %H:%M') }}</small>
                            {% else %}
                                <small class="text-muted">لم يسجل دخول</small>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <button type="button" class="btn btn-outline-primary" onclick="viewUser({{ user.id }})" title="عرض">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button type="button" class="btn btn-outline-secondary" onclick="editUser({{ user.id }})" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </button>
                                {% if user.id != current_user.id %}
                                <button type="button" class="btn btn-outline-warning" onclick="toggleUserStatus({{ user.id }}, {{ user.is_active|lower }})" title="تغيير الحالة">
                                    {% if user.is_active %}
                                        <i class="fas fa-user-slash"></i>
                                    {% else %}
                                        <i class="fas fa-user-check"></i>
                                    {% endif %}
                                </button>
                                <button type="button" class="btn btn-outline-danger" onclick="deleteUser({{ user.id }})" title="حذف">
                                    <i class="fas fa-trash"></i>
                                </button>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        {% if users.pages > 1 %}
        <nav aria-label="تصفح المستخدمين">
            <ul class="pagination justify-content-center">
                {% if users.has_prev %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('users', page=users.prev_num) }}">السابق</a>
                </li>
                {% endif %}
                
                {% for page_num in users.iter_pages() %}
                    {% if page_num %}
                        {% if page_num != users.page %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('users', page=page_num) }}">{{ page_num }}</a>
                        </li>
                        {% else %}
                        <li class="page-item active">
                            <span class="page-link">{{ page_num }}</span>
                        </li>
                        {% endif %}
                    {% else %}
                    <li class="page-item disabled">
                        <span class="page-link">…</span>
                    </li>
                    {% endif %}
                {% endfor %}
                
                {% if users.has_next %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('users', page=users.next_num) }}">التالي</a>
                </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}
        
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-users fa-4x text-muted mb-3"></i>
            <h4 class="text-muted">لا يوجد مستخدمين</h4>
            <p class="text-muted">لم يتم إضافة أي مستخدمين بعد.</p>
            <a href="{{ url_for('add_user') }}" class="btn btn-primary">
                <i class="fas fa-user-plus me-2"></i>
                إضافة أول مستخدم
            </a>
        </div>
        {% endif %}
    </div>
</div>

<!-- إحصائيات سريعة -->
<div class="row mt-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body text-center">
                <h3>{{ users.total }}</h3>
                <p class="mb-0">إجمالي المستخدمين</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <h3>{{ users.items | selectattr('is_active') | list | length }}</h3>
                <p class="mb-0">مستخدمين نشطين</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body text-center">
                <h3>{{ users.items | selectattr('role', 'equalto', 'admin') | list | length }}</h3>
                <p class="mb-0">مديري النظام</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body text-center">
                <h3>{{ users.items | selectattr('last_login') | list | length }}</h3>
                <p class="mb-0">سجلوا دخول</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function viewUser(userId) {
    // جلب بيانات المستخدم وعرضها في modal
    $.get('/api/users/' + userId)
        .done(function(user) {
            var modalContent = `
                <div class="modal fade" id="viewUserModal" tabindex="-1">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">تفاصيل المستخدم</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <div class="row">
                                    <div class="col-sm-4"><strong>اسم المستخدم:</strong></div>
                                    <div class="col-sm-8">${user.username}</div>
                                </div>
                                <hr>
                                <div class="row">
                                    <div class="col-sm-4"><strong>الاسم الكامل:</strong></div>
                                    <div class="col-sm-8">${user.full_name}</div>
                                </div>
                                <hr>
                                <div class="row">
                                    <div class="col-sm-4"><strong>البريد الإلكتروني:</strong></div>
                                    <div class="col-sm-8">${user.email}</div>
                                </div>
                                <hr>
                                <div class="row">
                                    <div class="col-sm-4"><strong>القسم:</strong></div>
                                    <div class="col-sm-8">${user.department}</div>
                                </div>
                                <hr>
                                <div class="row">
                                    <div class="col-sm-4"><strong>الدور:</strong></div>
                                    <div class="col-sm-8">
                                        ${user.role === 'admin' ? '<span class="badge bg-danger">مدير النظام</span>' :
                                          user.role === 'manager' ? '<span class="badge bg-warning">مدير</span>' :
                                          user.role === 'user' ? '<span class="badge bg-primary">مستخدم</span>' :
                                          '<span class="badge bg-secondary">مشاهد</span>'}
                                    </div>
                                </div>
                                <hr>
                                <div class="row">
                                    <div class="col-sm-4"><strong>الحالة:</strong></div>
                                    <div class="col-sm-8">
                                        ${user.is_active ? '<span class="badge bg-success">نشط</span>' : '<span class="badge bg-secondary">غير نشط</span>'}
                                    </div>
                                </div>
                                <hr>
                                <div class="row">
                                    <div class="col-sm-4"><strong>آخر دخول:</strong></div>
                                    <div class="col-sm-8">${user.last_login ? new Date(user.last_login).toLocaleString('ar-EG') : 'لم يسجل دخول'}</div>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                                <button type="button" class="btn btn-primary" onclick="editUser(${userId})">تعديل</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // إزالة أي modal موجود مسبقاً
            $('#viewUserModal').remove();

            // إضافة المحتوى الجديد وإظهار النافذة
            $('body').append(modalContent);
            $('#viewUserModal').modal('show');
        })
        .fail(function() {
            alert('حدث خطأ في جلب بيانات المستخدم');
        });
}

function editUser(userId) {
    // الانتقال إلى صفحة التعديل
    window.location.href = '/users/' + userId + '/edit';
}

function toggleUserStatus(userId, currentStatus) {
    var action = currentStatus ? 'إلغاء تفعيل' : 'تفعيل';
    if (confirm('هل تريد ' + action + ' هذا المستخدم؟')) {
        $.post('/api/users/' + userId + '/toggle-status')
            .done(function(response) {
                if (response.success) {
                    location.reload();
                } else {
                    alert('خطأ: ' + response.message);
                }
            })
            .fail(function(xhr) {
                var message = xhr.responseJSON ? xhr.responseJSON.message : 'حدث خطأ غير متوقع';
                alert('حدث خطأ: ' + message);
            });
    }
}

function deleteUser(userId) {
    if (confirm('هل أنت متأكد من حذف هذا المستخدم؟\nسيتم حذف جميع البيانات المرتبطة به نهائياً.')) {
        $.ajax({
            url: '/api/users/' + userId,
            type: 'DELETE',
            success: function(response) {
                if (response.success) {
                    location.reload();
                } else {
                    alert('خطأ: ' + response.message);
                }
            },
            error: function(xhr) {
                var message = xhr.responseJSON ? xhr.responseJSON.message : 'حدث خطأ غير متوقع';
                alert('حدث خطأ: ' + message);
            }
        });
    }
}
</script>
{% endblock %}
