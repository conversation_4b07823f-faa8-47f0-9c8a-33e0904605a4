{% extends "base.html" %}

{% block title %}إضافة كتاب جديد - نظام الأرشفة الإلكترونية{% endblock %}

{% block content %}
<div class="page-header">
    <h1><i class="fas fa-plus-circle me-2"></i>إضافة كتاب جديد</h1>
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ url_for('dashboard') }}">لوحة التحكم</a></li>
            <li class="breadcrumb-item"><a href="{{ url_for('documents') }}">الكتب</a></li>
            <li class="breadcrumb-item active">إضافة كتاب جديد</li>
        </ol>
    </nav>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-file-plus me-2"></i>
                    معلومات الكتاب
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" enctype="multipart/form-data">
                    {{ form.hidden_tag() }}
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.document_number.label(class="form-label") }}
                                {{ form.document_number(class="form-control" + (" is-invalid" if form.document_number.errors else "")) }}
                                {% if form.document_number.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.document_number.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.document_date.label(class="form-label") }}
                                {{ form.document_date(class="form-control" + (" is-invalid" if form.document_date.errors else "")) }}
                                {% if form.document_date.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.document_date.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        {{ form.subject.label(class="form-label") }}
                        {{ form.subject(class="form-control" + (" is-invalid" if form.subject.errors else "")) }}
                        {% if form.subject.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.subject.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.content.label(class="form-label") }}
                        {{ form.content(class="form-control", rows="4") }}
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                {{ form.document_type.label(class="form-label") }}
                                {{ form.document_type(class="form-select" + (" is-invalid" if form.document_type.errors else "")) }}
                                {% if form.document_type.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.document_type.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="mb-3">
                                {{ form.document_type_id.label(class="form-label") }}
                                {{ form.document_type_id(class="form-select") }}
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="mb-3">
                                {{ form.department_id.label(class="form-label") }}
                                {{ form.department_id(class="form-select") }}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.sender.label(class="form-label") }}
                                {{ form.sender(class="form-control") }}
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.receiver.label(class="form-label") }}
                                {{ form.receiver(class="form-control") }}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.classification.label(class="form-label") }}
                                {{ form.classification(class="form-select") }}
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.priority.label(class="form-label") }}
                                {{ form.priority(class="form-select") }}
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        {{ form.attachments.label(class="form-label") }}
                        <div class="input-group">
                            {{ form.attachments(class="form-control") }}
                            <button type="button" class="btn scanner-btn" onclick="openScannerModal()" title="مسح ضوئي مباشر">
                                <i class="fas fa-print me-1"></i>
                                مسح ضوئي
                            </button>
                        </div>
                        <div class="form-text">أنواع الملفات المسموحة: PDF, DOC, DOCX, JPG, PNG, GIF (الحد الأقصى: 16 ميجابايت)</div>

                        <!-- منطقة عرض الصور الممسوحة -->
                        <div id="scannedImagesArea" class="mt-3" style="display: none;">
                            <h6>الصور الممسوحة:</h6>
                            <div id="scannedImagesList" class="row"></div>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('documents') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-right me-2"></i>
                            إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            حفظ الكتاب
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    إرشادات
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6><i class="fas fa-lightbulb me-2"></i>نصائح مهمة:</h6>
                    <ul class="mb-0">
                        <li>تأكد من صحة رقم الكتاب وتاريخه</li>
                        <li>اكتب موضوعاً واضحاً ومفهوماً</li>
                        <li>حدد نوع الكتاب بدقة</li>
                        <li>أضف المرفقات إذا كانت متوفرة</li>
                        <li>حدد درجة السرية والأولوية المناسبة</li>
                    </ul>
                </div>
                
                <div class="alert alert-warning">
                    <h6><i class="fas fa-exclamation-triangle me-2"></i>تنبيه:</h6>
                    <p class="mb-0">تأكد من مراجعة جميع البيانات قبل الحفظ، حيث أن بعض المعلومات قد تحتاج إلى صلاحيات خاصة للتعديل لاحقاً.</p>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-keyboard me-2"></i>
                    اختصارات لوحة المفاتيح
                </h5>
            </div>
            <div class="card-body">
                <small>
                    <strong>Ctrl + S:</strong> حفظ الكتاب<br>
                    <strong>Ctrl + Z:</strong> تراجع<br>
                    <strong>Tab:</strong> الانتقال للحقل التالي
                </small>
            </div>
        </div>
    </div>
</div>

<!-- نافذة السكانر المنبثقة -->
<div class="modal fade" id="scannerModal" tabindex="-1" aria-labelledby="scannerModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="scannerModalLabel">
                    <i class="fas fa-scanner me-2"></i>
                    المسح الضوئي
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- اكتشاف السكانرات -->
                <div id="scannerDetection">
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">جاري البحث عن السكانرات...</span>
                        </div>
                        <p class="mt-2">جاري البحث عن السكانرات المتاحة...</p>
                    </div>
                </div>

                <!-- قائمة السكانرات -->
                <div id="scannersList" style="display: none;">
                    <div class="mb-3">
                        <label for="scannerSelect" class="form-label">اختر السكانر:</label>
                        <select class="form-select" id="scannerSelect">
                            <option value="">اختر السكانر...</option>
                        </select>
                    </div>

                    <!-- إعدادات المسح -->
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="scanResolution" class="form-label">الدقة (DPI):</label>
                                <select class="form-select" id="scanResolution">
                                    <option value="150">150 DPI</option>
                                    <option value="300" selected>300 DPI</option>
                                    <option value="600">600 DPI</option>
                                    <option value="1200">1200 DPI</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="scanMode" class="form-label">نوع المسح:</label>
                                <select class="form-select" id="scanMode">
                                    <option value="color" selected>ملون</option>
                                    <option value="gray">رمادي</option>
                                    <option value="lineart">أبيض وأسود</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="scanFormat" class="form-label">تنسيق الحفظ:</label>
                                <select class="form-select" id="scanFormat">
                                    <option value="JPEG" selected>JPEG</option>
                                    <option value="PNG">PNG</option>
                                    <option value="PDF">PDF</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- أزرار التحكم -->
                    <div class="d-grid gap-2">
                        <button type="button" class="btn btn-primary" onclick="startScan()">
                            <i class="fas fa-play me-2"></i>
                            بدء المسح
                        </button>
                    </div>
                </div>

                <!-- حالة المسح -->
                <div id="scanProgress" style="display: none;">
                    <div class="text-center">
                        <div class="spinner-border text-success" role="status">
                            <span class="visually-hidden">جاري المسح...</span>
                        </div>
                        <p class="mt-2">جاري مسح المستند... يرجى الانتظار</p>
                    </div>
                </div>

                <!-- معاينة النتيجة -->
                <div id="scanResult" style="display: none;">
                    <h6>معاينة الصورة الممسوحة:</h6>
                    <div class="text-center">
                        <img id="scannedPreview" class="img-fluid border rounded" style="max-height: 400px;" alt="معاينة المسح">
                    </div>
                    <div class="mt-3">
                        <div class="row">
                            <div class="col-md-6">
                                <label for="scannedFileName" class="form-label">اسم الملف:</label>
                                <input type="text" class="form-control" id="scannedFileName" placeholder="اسم الملف">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">معلومات الملف:</label>
                                <div id="fileInfo" class="form-text"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-success" id="addScannedBtn" onclick="addScannedImage()" style="display: none;">
                    <i class="fas fa-plus me-2"></i>
                    إضافة الصورة
                </button>
                <button type="button" class="btn btn-warning" id="rescanBtn" onclick="resetScanner()" style="display: none;">
                    <i class="fas fa-redo me-2"></i>
                    مسح مرة أخرى
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// متغيرات عامة للسكانر
let availableScanners = [];
let scannedImages = [];
let currentScanResult = null;

$(document).ready(function() {
    // اختصار لوحة المفاتيح للحفظ
    $(document).keydown(function(e) {
        if (e.ctrlKey && e.keyCode == 83) { // Ctrl+S
            e.preventDefault();
            $('form').submit();
        }
    });

    // تحديث الحقول حسب نوع الكتاب
    $('#document_type').change(function() {
        var type = $(this).val();
        if (type === 'incoming') {
            $('#sender').closest('.mb-3').show();
            $('#receiver').closest('.mb-3').hide();
            $('#sender').attr('required', true);
            $('#receiver').attr('required', false);
        } else if (type === 'outgoing') {
            $('#sender').closest('.mb-3').hide();
            $('#receiver').closest('.mb-3').show();
            $('#sender').attr('required', false);
            $('#receiver').attr('required', true);
        } else {
            $('#sender').closest('.mb-3').show();
            $('#receiver').closest('.mb-3').show();
            $('#sender').attr('required', false);
            $('#receiver').attr('required', false);
        }
    });

    // تشغيل التحديث عند تحميل الصفحة
    $('#document_type').trigger('change');
});

// فتح نافذة السكانر
function openScannerModal() {
    $('#scannerModal').modal('show');
    detectScanners();
}

// اكتشاف السكانرات
function detectScanners() {
    $('#scannerDetection').show();
    $('#scannersList').hide();
    $('#scanProgress').hide();
    $('#scanResult').hide();
    $('#addScannedBtn').hide();
    $('#rescanBtn').hide();

    $.get('/api/scanner/detect')
        .done(function(response) {
            if (response.success && response.scanners.length > 0) {
                availableScanners = response.scanners;
                populateScannersList();
                $('#scannerDetection').hide();
                $('#scannersList').show();
            } else {
                $('#scannerDetection').html(`
                    <div class="alert alert-warning text-center">
                        <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                        <h6>لم يتم العثور على سكانرات</h6>
                        <p>تأكد من توصيل السكانر وتثبيت التعريفات المناسبة</p>
                        <button type="button" class="btn btn-primary btn-sm" onclick="detectScanners()">
                            <i class="fas fa-redo me-1"></i>
                            إعادة البحث
                        </button>
                    </div>
                `);
            }
        })
        .fail(function() {
            $('#scannerDetection').html(`
                <div class="alert alert-danger text-center">
                    <i class="fas fa-times-circle fa-2x mb-2"></i>
                    <h6>خطأ في اكتشاف السكانرات</h6>
                    <p>حدث خطأ أثناء البحث عن السكانرات المتاحة</p>
                    <button type="button" class="btn btn-primary btn-sm" onclick="detectScanners()">
                        <i class="fas fa-redo me-1"></i>
                        إعادة المحاولة
                    </button>
                </div>
            `);
        });
}

// ملء قائمة السكانرات
function populateScannersList() {
    const select = $('#scannerSelect');
    select.empty().append('<option value="">اختر السكانر...</option>');

    availableScanners.forEach(function(scanner) {
        select.append(`<option value="${scanner.id}">${scanner.name} (${scanner.vendor})</option>`);
    });

    // اختيار أول سكانر تلقائياً
    if (availableScanners.length > 0) {
        select.val(availableScanners[0].id);
    }
}

// بدء المسح
function startScan() {
    const scannerId = $('#scannerSelect').val();
    if (!scannerId) {
        alert('يرجى اختيار السكانر أولاً');
        return;
    }

    $('#scannersList').hide();
    $('#scanProgress').show();
    $('#scanResult').hide();

    const formData = new FormData();
    formData.append('scanner_id', scannerId);
    formData.append('resolution', $('#scanResolution').val());
    formData.append('mode', $('#scanMode').val());
    formData.append('format', $('#scanFormat').val());

    $.ajax({
        url: '/api/scanner/scan',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        timeout: 60000, // 60 ثانية
        success: function(response) {
            if (response.success) {
                currentScanResult = response;
                showScanResult(response);
            } else {
                showScanError(response.message);
            }
        },
        error: function(xhr) {
            let message = 'حدث خطأ أثناء المسح';
            if (xhr.responseJSON && xhr.responseJSON.message) {
                message = xhr.responseJSON.message;
            }
            showScanError(message);
        }
    });
}

// عرض نتيجة المسح
function showScanResult(result) {
    $('#scanProgress').hide();
    $('#scanResult').show();
    $('#addScannedBtn').show();
    $('#rescanBtn').show();

    // عرض الصورة
    const previewUrl = `/api/scanner/preview/${result.filename}`;
    $('#scannedPreview').attr('src', previewUrl);

    // تعيين اسم الملف
    const defaultName = `مسح_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}`;
    $('#scannedFileName').val(defaultName);

    // عرض معلومات الملف
    const sizeKB = Math.round(result.size / 1024);
    let fileInfo = `الحجم: ${sizeKB} كيلوبايت`;
    if (result.demo) {
        fileInfo += ' (مسح تجريبي)';
    }
    $('#fileInfo').text(fileInfo);
}

// عرض خطأ المسح
function showScanError(message) {
    $('#scanProgress').hide();
    $('#scannersList').show();

    const alertHtml = `
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    $('#scannersList').prepend(alertHtml);
}

// إضافة الصورة الممسوحة
function addScannedImage() {
    if (!currentScanResult) return;

    const fileName = $('#scannedFileName').val() || 'مسح_جديد';
    const imageData = {
        filename: currentScanResult.filename,
        displayName: fileName,
        size: currentScanResult.size,
        demo: currentScanResult.demo || false
    };

    scannedImages.push(imageData);
    updateScannedImagesList();

    $('#scannerModal').modal('hide');

    // إظهار رسالة نجاح
    const alertHtml = `
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            تم إضافة الصورة الممسوحة بنجاح
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    $('.card-body').prepend(alertHtml);
}

// تحديث قائمة الصور الممسوحة
function updateScannedImagesList() {
    const container = $('#scannedImagesList');
    container.empty();

    if (scannedImages.length > 0) {
        $('#scannedImagesArea').show();

        scannedImages.forEach(function(image, index) {
            const sizeKB = Math.round(image.size / 1024);
            const demoLabel = image.demo ? '<span class="badge bg-warning">تجريبي</span>' : '';

            const imageCard = `
                <div class="col-md-4 mb-2">
                    <div class="card border">
                        <div class="card-body p-2">
                            <div class="d-flex align-items-center">
                                <div class="me-2">
                                    <i class="fas fa-image fa-2x text-primary"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-1">${image.displayName}</h6>
                                    <small class="text-muted">${sizeKB} كيلوبايت ${demoLabel}</small>
                                </div>
                                <div>
                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeScannedImage(${index})">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            container.append(imageCard);
        });
    } else {
        $('#scannedImagesArea').hide();
    }
}

// حذف صورة ممسوحة
function removeScannedImage(index) {
    if (confirm('هل تريد حذف هذه الصورة؟')) {
        scannedImages.splice(index, 1);
        updateScannedImagesList();
    }
}

// إعادة تعيين السكانر
function resetScanner() {
    $('#scanResult').hide();
    $('#addScannedBtn').hide();
    $('#rescanBtn').hide();
    $('#scannersList').show();
    currentScanResult = null;
}

// تحديث النموذج عند الإرسال لتضمين الصور الممسوحة
$('form').on('submit', function(e) {
    if (scannedImages.length > 0) {
        // إضافة معلومات الصور الممسوحة كحقول مخفية
        scannedImages.forEach(function(image, index) {
            $('<input>').attr({
                type: 'hidden',
                name: `scanned_images[${index}][filename]`,
                value: image.filename
            }).appendTo(this);

            $('<input>').attr({
                type: 'hidden',
                name: `scanned_images[${index}][display_name]`,
                value: image.displayName
            }).appendTo(this);
        });
    }
});
</script>
{% endblock %}
