<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نموذج التقارير</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>اختبار نموذج التقارير</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="http://127.0.0.1:5000/reports/generate">
                            <input type="hidden" name="csrf_token" value="test-token"  />
                            <div class="mb-3">
                                <label for="report_type" class="form-label">نوع التقرير</label>
                                <select name="report_type" id="report_type" class="form-select" required>
                                    <option value="documents_summary">ملخص الكتب</option>
                                    <option value="documents_by_type">الكتب حسب النوع</option>
                                    <option value="documents_by_department">الكتب حسب القسم</option>
                                    <option value="user_activity">نشاط المستخدمين</option>
                                </select>
                            </div>
                            
                            <div class="row">
                                <div class="col-6">
                                    <div class="mb-3">
                                        <label for="date_from" class="form-label">من تاريخ</label>
                                        <input type="date" name="date_from" id="date_from" class="form-control" required>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="mb-3">
                                        <label for="date_to" class="form-label">إلى تاريخ</label>
                                        <input type="date" name="date_to" id="date_to" class="form-control" required>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="department_id" class="form-label">القسم</label>
                                <select name="department_id" id="department_id" class="form-select">
                                    <option value="0">جميع الأقسام</option>
                                    <option value="1">الحسابات</option>
                                    <option value="2">التدقيق</option>
                                    <option value="3">تخطيط</option>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label for="document_type" class="form-label">نوع الكتاب</label>
                                <select name="document_type" id="document_type" class="form-select">
                                    <option value="">جميع الأنواع</option>
                                    <option value="incoming">وارد</option>
                                    <option value="outgoing">صادر</option>
                                    <option value="internal">داخلي</option>
                                </select>
                            </div>
                            
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    إنشاء التقرير
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // تعيين التواريخ الافتراضية
        document.addEventListener('DOMContentLoaded', function() {
            var today = new Date();
            var monthAgo = new Date(today.getFullYear(), today.getMonth() - 1, today.getDate());
            
            document.getElementById('date_from').value = monthAgo.toISOString().split('T')[0];
            document.getElementById('date_to').value = today.toISOString().split('T')[0];
        });
    </script>
</body>
</html>
