{% extends "base.html" %}

{% block title %}تعديل الكتاب {{ document.get_full_number() }} - نظام الأرشفة الإلكترونية{% endblock %}

{% block content %}
<div class="page-header">
    <h1><i class="fas fa-edit me-2"></i>تعديل الكتاب {{ document.get_full_number() }}</h1>
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ url_for('dashboard') }}">لوحة التحكم</a></li>
            <li class="breadcrumb-item"><a href="{{ url_for('documents') }}">الكتب</a></li>
            <li class="breadcrumb-item"><a href="{{ url_for('view_document', id=document.id) }}">{{ document.get_full_number() }}</a></li>
            <li class="breadcrumb-item active">تعديل</li>
        </ol>
    </nav>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-file-edit me-2"></i>
                    تعديل معلومات الكتاب
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" enctype="multipart/form-data">
                    {{ form.hidden_tag() }}
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.document_number.label(class="form-label") }}
                                {{ form.document_number(class="form-control" + (" is-invalid" if form.document_number.errors else "")) }}
                                {% if form.document_number.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.document_number.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.document_date.label(class="form-label") }}
                                {{ form.document_date(class="form-control" + (" is-invalid" if form.document_date.errors else "")) }}
                                {% if form.document_date.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.document_date.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        {{ form.subject.label(class="form-label") }}
                        {{ form.subject(class="form-control" + (" is-invalid" if form.subject.errors else "")) }}
                        {% if form.subject.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.subject.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.content.label(class="form-label") }}
                        {{ form.content(class="form-control", rows="4") }}
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                {{ form.document_type.label(class="form-label") }}
                                {{ form.document_type(class="form-select" + (" is-invalid" if form.document_type.errors else "")) }}
                                {% if form.document_type.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.document_type.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="mb-3">
                                {{ form.document_type_id.label(class="form-label") }}
                                {{ form.document_type_id(class="form-select") }}
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="mb-3">
                                {{ form.department_id.label(class="form-label") }}
                                {{ form.department_id(class="form-select") }}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.sender.label(class="form-label") }}
                                {{ form.sender(class="form-control") }}
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.receiver.label(class="form-label") }}
                                {{ form.receiver(class="form-control") }}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.classification.label(class="form-label") }}
                                {{ form.classification(class="form-select") }}
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.priority.label(class="form-label") }}
                                {{ form.priority(class="form-select") }}
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        {{ form.attachments.label(class="form-label") }}
                        {{ form.attachments(class="form-control") }}
                        <div class="form-text">إضافة مرفق جديد (اختياري)</div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('view_document', id=document.id) }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-right me-2"></i>
                            إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            حفظ التعديلات
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <!-- المرفقات الحالية -->
        {% if document.has_attachments %}
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-paperclip me-2"></i>
                    المرفقات الحالية
                </h6>
            </div>
            <div class="card-body">
                {% for attachment in document.attachments %}
                <div class="d-flex align-items-center mb-2">
                    <div class="me-2">
                        {% if attachment.file_type.startswith('image/') %}
                            <i class="fas fa-image text-primary"></i>
                        {% elif 'pdf' in attachment.file_type %}
                            <i class="fas fa-file-pdf text-danger"></i>
                        {% else %}
                            <i class="fas fa-file text-secondary"></i>
                        {% endif %}
                    </div>
                    <div class="flex-grow-1">
                        <small>{{ attachment.original_filename }}</small>
                    </div>
                    <div>
                        <a href="{{ url_for('download_attachment', id=attachment.id) }}" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-download"></i>
                        </a>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}
        
        <!-- معلومات إضافية -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات إضافية
                </h6>
            </div>
            <div class="card-body">
                <small>
                    <strong>أنشئ بواسطة:</strong> {{ document.creator.full_name }}<br>
                    <strong>تاريخ الإنشاء:</strong> {{ document.created_at.strftime('%Y-%m-%d %H:%M') }}<br>
                    {% if document.updated_at != document.created_at %}
                    <strong>آخر تحديث:</strong> {{ document.updated_at.strftime('%Y-%m-%d %H:%M') }}<br>
                    {% endif %}
                    <strong>عدد المرفقات:</strong> {{ document.attachments_count }}<br>
                    <strong>عدد الحركات:</strong> {{ document.movements.count() }}
                </small>
            </div>
        </div>
        
        <!-- تحذير -->
        <div class="alert alert-warning mt-3">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <strong>تنبيه:</strong> سيتم تسجيل جميع التعديلات في سجل العمليات.
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // تحديث الحقول حسب نوع الكتاب
    $('#document_type').change(function() {
        var type = $(this).val();
        if (type === 'incoming') {
            $('#sender').closest('.mb-3').show();
            $('#receiver').closest('.mb-3').hide();
        } else if (type === 'outgoing') {
            $('#sender').closest('.mb-3').hide();
            $('#receiver').closest('.mb-3').show();
        } else {
            $('#sender').closest('.mb-3').show();
            $('#receiver').closest('.mb-3').show();
        }
    });
    
    // تشغيل التحديث عند تحميل الصفحة
    $('#document_type').trigger('change');
});
</script>
{% endblock %}
