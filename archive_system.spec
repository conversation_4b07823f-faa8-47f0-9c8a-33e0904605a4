# -*- mode: python ; coding: utf-8 -*-

import os
import sys
from PyInstaller.utils.hooks import collect_data_files, collect_submodules

# تحديد المسار الأساسي
block_cipher = None
base_path = os.path.abspath('.')

# جمع ملفات البيانات للمكتبات
datas = []

# إضافة قوالب Flask
datas += [('templates', 'templates')]

# إضافة الملفات الثابتة
datas += [('static', 'static')]

# إضافة ملفات التكوين
if os.path.exists('instance'):
    datas += [('instance', 'instance')]

# إضافة مجلد الرفع إذا كان موجوداً
if os.path.exists('uploads'):
    datas += [('uploads', 'uploads')]

# إضافة مجلد النسخ الاحتياطية إذا كان موجوداً
if os.path.exists('backups'):
    datas += [('backups', 'backups')]

# جمع ملفات البيانات للمكتبات المطلوبة
datas += collect_data_files('flask')
datas += collect_data_files('flask_sqlalchemy')
datas += collect_data_files('flask_login')
datas += collect_data_files('flask_wtf')
datas += collect_data_files('wtforms')
datas += collect_data_files('reportlab')
datas += collect_data_files('openpyxl')

# جمع الوحدات المخفية
hiddenimports = []
hiddenimports += collect_submodules('flask')
hiddenimports += collect_submodules('flask_sqlalchemy')
hiddenimports += collect_submodules('flask_login')
hiddenimports += collect_submodules('flask_wtf')
hiddenimports += collect_submodules('wtforms')
hiddenimports += collect_submodules('reportlab')
hiddenimports += collect_submodules('openpyxl')
hiddenimports += collect_submodules('PIL')
hiddenimports += collect_submodules('cv2')

# إضافة وحدات إضافية مطلوبة
hiddenimports += [
    'email.mime.multipart',
    'email.mime.text',
    'email.mime.base',
    'sqlite3',
    'datetime',
    'os',
    'sys',
    'json',
    'base64',
    'hashlib',
    'secrets',
    'werkzeug.security',
    'sqlalchemy.dialects.sqlite',
    'reportlab.pdfbase.ttfonts',
    'reportlab.lib.fonts',
    'reportlab.pdfbase.pdfmetrics',
    'reportlab.platypus',
    'reportlab.lib.styles',
    'reportlab.lib.units',
    'reportlab.lib.pagesizes',
    'reportlab.graphics.shapes',
    'reportlab.graphics.charts.linecharts',
    'reportlab.graphics.charts.piecharts',
    'reportlab.graphics.charts.barcharts',
]

a = Analysis(
    ['app.py'],
    pathex=[base_path],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'tkinter',
        'matplotlib',
        'numpy',
        'scipy',
        'pandas',
        'jupyter',
        'IPython',
        'notebook',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='ArchiveSystem',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='static/favicon.ico' if os.path.exists('static/favicon.ico') else None,
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='ArchiveSystem',
)
