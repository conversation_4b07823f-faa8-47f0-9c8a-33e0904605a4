{% extends "base.html" %}

{% block title %}إضافة مستخدم جديد - نظام الأرشفة الإلكترونية{% endblock %}

{% block content %}
<div class="page-header">
    <h1><i class="fas fa-user-plus me-2"></i>إضافة مستخدم جديد</h1>
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ url_for('dashboard') }}">لوحة التحكم</a></li>
            <li class="breadcrumb-item"><a href="{{ url_for('users') }}">إدارة المستخدمين</a></li>
            <li class="breadcrumb-item active">إضافة مستخدم جديد</li>
        </ol>
    </nav>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-user-cog me-2"></i>
                    معلومات المستخدم
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    {{ form.hidden_tag() }}
                    
                    <div class="mb-3">
                        {{ form.username.label(class="form-label") }}
                        {{ form.username(class="form-control" + (" is-invalid" if form.username.errors else "")) }}
                        {% if form.username.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.username.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                        <div class="form-text">اسم المستخدم للدخول إلى النظام</div>
                    </div>
                    
                    <div class="mb-3">
                        {{ form.full_name.label(class="form-label") }}
                        {{ form.full_name(class="form-control" + (" is-invalid" if form.full_name.errors else "")) }}
                        {% if form.full_name.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.full_name.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.password.label(class="form-label") }}
                                {{ form.password(class="form-control" + (" is-invalid" if form.password.errors else "")) }}
                                {% if form.password.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.password.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                                <div class="form-text">اتركه فارغاً لاستخدام كلمة المرور الافتراضية: 123456</div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.role.label(class="form-label") }}
                                {{ form.role(class="form-select" + (" is-invalid" if form.role.errors else "")) }}
                                {% if form.role.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.role.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        {{ form.department.label(class="form-label") }}
                        {{ form.department(class="form-control" + (" is-invalid" if form.department.errors else "")) }}
                        {% if form.department.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.department.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="form-check mb-3">
                        {{ form.is_active(class="form-check-input") }}
                        {{ form.is_active.label(class="form-check-label") }}
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('users') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-right me-2"></i>
                            إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-user-plus me-2"></i>
                            إضافة المستخدم
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <!-- معلومات الأدوار -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    أدوار المستخدمين
                </h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <span class="badge bg-danger me-2">مدير النظام</span>
                    <small>صلاحيات كاملة لإدارة النظام والمستخدمين</small>
                </div>
                
                <div class="mb-3">
                    <span class="badge bg-warning me-2">مدير</span>
                    <small>إدارة الكتب والتقارير وعرض الإحصائيات</small>
                </div>
                
                <div class="mb-3">
                    <span class="badge bg-primary me-2">مستخدم</span>
                    <small>إضافة وتعديل الكتب والبحث</small>
                </div>
                
                <div class="mb-3">
                    <span class="badge bg-secondary me-2">مشاهد</span>
                    <small>عرض الكتب والبحث فقط</small>
                </div>
            </div>
        </div>
        
        <!-- نصائح -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-lightbulb me-2"></i>
                    نصائح مهمة
                </h6>
            </div>
            <div class="card-body">
                <ul class="mb-0">
                    <li>اختر اسم مستخدم واضح ومفهوم</li>
                    <li>تأكد من صحة البريد الإلكتروني</li>
                    <li>حدد الدور المناسب للمستخدم</li>
                    <li>يمكن تغيير كلمة المرور لاحقاً</li>
                    <li>يمكن تعطيل المستخدم مؤقتاً</li>
                </ul>
            </div>
        </div>
        
        <!-- إحصائيات -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    إحصائيات المستخدمين
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border rounded p-2">
                            <h5 class="text-primary mb-0">0</h5>
                            <small class="text-muted">مديرين</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="border rounded p-2">
                            <h5 class="text-success mb-0">0</h5>
                            <small class="text-muted">مستخدمين</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // تحديث معلومات الدور عند التغيير
    $('#role').change(function() {
        var role = $(this).val();
        var description = '';
        
        switch(role) {
            case 'admin':
                description = 'صلاحيات كاملة لإدارة النظام والمستخدمين والإعدادات';
                break;
            case 'manager':
                description = 'إدارة الكتب والتقارير وعرض الإحصائيات';
                break;
            case 'user':
                description = 'إضافة وتعديل الكتب والبحث والتحويل';
                break;
            case 'viewer':
                description = 'عرض الكتب والبحث فقط بدون تعديل';
                break;
        }
        
        // يمكن إضافة عرض الوصف في مكان مناسب
    });
    
    // التحقق من قوة كلمة المرور
    $('#password').on('input', function() {
        var password = $(this).val();
        var strength = 0;
        
        if (password.length >= 6) strength++;
        if (password.match(/[a-z]/)) strength++;
        if (password.match(/[A-Z]/)) strength++;
        if (password.match(/[0-9]/)) strength++;
        
        var strengthText = ['ضعيفة جداً', 'ضعيفة', 'متوسطة', 'قوية', 'قوية جداً'];
        var strengthColor = ['danger', 'warning', 'info', 'success', 'success'];
        
        if (password.length > 0) {
            $(this).next('.form-text').html(
                '<span class="text-' + strengthColor[strength] + '">قوة كلمة المرور: ' + strengthText[strength] + '</span>'
            );
        }
    });
});
</script>
{% endblock %}
