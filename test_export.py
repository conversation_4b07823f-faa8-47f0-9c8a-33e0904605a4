#!/usr/bin/env python3
"""
اختبار وظائف التصدير
"""

import openpyxl
import io
from datetime import datetime

def test_excel_export():
    """اختبار إنشاء ملف Excel"""
    try:
        print("بدء اختبار إنشاء ملف Excel...")
        
        # إنشاء ملف Excel
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "اختبار"
        
        # إضافة بيانات
        ws['A1'] = "اختبار التصدير"
        ws['A2'] = f"التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M')}"
        ws['A3'] = "رقم"
        ws['B3'] = "الوصف"
        
        # إضافة بيانات تجريبية
        for i in range(1, 6):
            ws[f'A{i+3}'] = f"TEST-{i:03d}"
            ws[f'B{i+3}'] = f"وصف تجريبي {i}"
        
        # حفظ في ملف
        filename = f"test_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        wb.save(filename)
        print(f"تم إنشاء الملف: {filename}")
        
        # اختبار الحفظ في الذاكرة
        output = io.BytesIO()
        wb.save(output)
        output.seek(0)
        print(f"حجم الملف في الذاكرة: {len(output.getvalue())} بايت")
        
        return True
        
    except Exception as e:
        print(f"خطأ في اختبار Excel: {e}")
        return False

def test_pdf_export():
    """اختبار إنشاء ملف PDF"""
    try:
        print("بدء اختبار إنشاء ملف PDF...")
        
        from reportlab.lib.pagesizes import A4
        from reportlab.lib import colors
        from reportlab.lib.styles import getSampleStyleSheet
        from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph
        import io
        
        # إنشاء ملف PDF في الذاكرة
        output = io.BytesIO()
        doc = SimpleDocTemplate(output, pagesize=A4)
        
        # إعداد المحتوى
        styles = getSampleStyleSheet()
        story = []
        
        # العنوان
        title = Paragraph("اختبار تصدير PDF", styles['Title'])
        story.append(title)
        
        # جدول بيانات
        data = [
            ['رقم', 'الوصف', 'التاريخ'],
            ['TEST-001', 'اختبار 1', '2024-12-01'],
            ['TEST-002', 'اختبار 2', '2024-12-02'],
            ['TEST-003', 'اختبار 3', '2024-12-03']
        ]
        
        table = Table(data)
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 14),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        story.append(table)
        
        # بناء المستند
        doc.build(story)
        output.seek(0)
        
        print(f"حجم ملف PDF في الذاكرة: {len(output.getvalue())} بايت")
        
        # حفظ في ملف
        filename = f"test_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
        with open(filename, 'wb') as f:
            f.write(output.getvalue())
        print(f"تم إنشاء ملف PDF: {filename}")
        
        return True
        
    except Exception as e:
        print(f"خطأ في اختبار PDF: {e}")
        return False

if __name__ == "__main__":
    print("=== اختبار وظائف التصدير ===")
    
    # اختبار Excel
    excel_result = test_excel_export()
    print(f"نتيجة اختبار Excel: {'نجح' if excel_result else 'فشل'}")
    print()
    
    # اختبار PDF
    pdf_result = test_pdf_export()
    print(f"نتيجة اختبار PDF: {'نجح' if pdf_result else 'فشل'}")
    print()
    
    if excel_result and pdf_result:
        print("✅ جميع الاختبارات نجحت!")
    else:
        print("❌ بعض الاختبارات فشلت!")
