[Setup]
; معلومات التطبيق الأساسية
AppName=نظام الأرشفة الإلكترونية
AppVersion=1.0.0
AppPublisher=Electronic Archive System
AppPublisherURL=https://github.com/safaa2009
AppSupportURL=https://github.com/safaa2009
AppUpdatesURL=https://github.com/safaa2009
DefaultDirName={autopf}\ArchiveSystem
DefaultGroupName=نظام الأرشفة الإلكترونية
AllowNoIcons=yes
LicenseFile=
InfoBeforeFile=
InfoAfterFile=
OutputDir=installer_output
OutputBaseFilename=ArchiveSystem_Setup_v1.0
SetupIconFile=
Compression=lzma
SolidCompression=yes
WizardStyle=modern
ArchitecturesAllowed=x64compatible
ArchitecturesInstallIn64BitMode=x64compatible

; متطلبات النظام
MinVersion=10.0.17763
OnlyBelowVersion=0

; إعدادات اللغة والترميز
LanguageDetectionMethod=uilanguage
ShowLanguageDialog=auto

; إعدادات الأذونات
PrivilegesRequired=admin
PrivilegesRequiredOverridesAllowed=dialog

; إعدادات إضافية
DisableProgramGroupPage=yes
DisableReadyPage=no
DisableFinishedPage=no
DisableWelcomePage=no
ShowTasksTreeLines=yes
AlwaysShowDirOnReadyPage=yes
AlwaysShowGroupOnReadyPage=yes

[Languages]
Name: "arabic"; MessagesFile: "compiler:Default.isl"
Name: "english"; MessagesFile: "compiler:Default.isl"

[Tasks]
Name: "desktopicon"; Description: "{cm:CreateDesktopIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked
Name: "quicklaunchicon"; Description: "{cm:CreateQuickLaunchIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked; OnlyBelowVersion: 6.1; Check: not IsAdminInstallMode

[Files]
; الملف التنفيذي الرئيسي
Source: "dist\ArchiveSystem\ArchiveSystem.exe"; DestDir: "{app}"; Flags: ignoreversion
; مجلد الملفات الداخلية
Source: "dist\ArchiveSystem\_internal\*"; DestDir: "{app}\_internal"; Flags: ignoreversion recursesubdirs createallsubdirs
; ملفات التشغيل والتوثيق
Source: "dist\ArchiveSystem\start_archive_system.bat"; DestDir: "{app}"; Flags: ignoreversion
Source: "dist\ArchiveSystem\README_INSTALLATION.md"; DestDir: "{app}"; Flags: ignoreversion

[Icons]
; أيقونة في قائمة البرامج
Name: "{group}\نظام الأرشفة الإلكترونية"; Filename: "{app}\ArchiveSystem.exe"
Name: "{group}\تشغيل سريع"; Filename: "{app}\start_archive_system.bat"
Name: "{group}\دليل الاستخدام"; Filename: "{app}\README_INSTALLATION.md"
Name: "{group}\{cm:UninstallProgram,نظام الأرشفة الإلكترونية}"; Filename: "{uninstallexe}"

; أيقونة على سطح المكتب
Name: "{autodesktop}\نظام الأرشفة الإلكترونية"; Filename: "{app}\ArchiveSystem.exe"; Tasks: desktopicon

; أيقونة في شريط المهام السريع
Name: "{userappdata}\Microsoft\Internet Explorer\Quick Launch\نظام الأرشفة الإلكترونية"; Filename: "{app}\ArchiveSystem.exe"; Tasks: quicklaunchicon

[Run]
; تشغيل التطبيق بعد التثبيت
Filename: "{app}\ArchiveSystem.exe"; Description: "{cm:LaunchProgram,نظام الأرشفة الإلكترونية}"; Flags: nowait postinstall skipifsilent

[UninstallDelete]
; حذف الملفات المُنشأة أثناء التشغيل
Type: filesandordirs; Name: "{app}\instance"
Type: filesandordirs; Name: "{app}\uploads"
Type: filesandordirs; Name: "{app}\backups"
Type: filesandordirs; Name: "{app}\logs"

[Code]
// كود Pascal للتحقق من متطلبات النظام
function InitializeSetup(): Boolean;
var
  Version: TWindowsVersion;
begin
  GetWindowsVersionEx(Version);
  
  // التحقق من إصدار Windows
  if Version.Major < 10 then
  begin
    MsgBox('هذا التطبيق يتطلب Windows 10 أو أحدث.' + #13#10 + 
           'This application requires Windows 10 or later.', 
           mbError, MB_OK);
    Result := False;
    Exit;
  end;
  
  // التحقق من المعمارية
  if not Is64BitInstallMode then
  begin
    MsgBox('هذا التطبيق يتطلب نظام تشغيل 64-bit.' + #13#10 + 
           'This application requires a 64-bit operating system.', 
           mbError, MB_OK);
    Result := False;
    Exit;
  end;
  
  Result := True;
end;

// إنشاء المجلدات المطلوبة بعد التثبيت
procedure CurStepChanged(CurStep: TSetupStep);
begin
  if CurStep = ssPostInstall then
  begin
    // إنشاء مجلدات البيانات
    CreateDir(ExpandConstant('{app}\instance'));
    CreateDir(ExpandConstant('{app}\uploads'));
    CreateDir(ExpandConstant('{app}\backups'));
    CreateDir(ExpandConstant('{app}\logs'));
  end;
end;

// رسالة ترحيب مخصصة
function NextButtonClick(CurPageID: Integer): Boolean;
begin
  Result := True;
  
  if CurPageID = wpWelcome then
  begin
    MsgBox('مرحباً بك في مثبت نظام الأرشفة الإلكترونية!' + #13#10 + #13#10 +
           'سيقوم هذا المثبت بتثبيت النظام على جهازك مع جميع المتطلبات.' + #13#10 + #13#10 +
           'Welcome to Electronic Archive System installer!' + #13#10 + #13#10 +
           'This installer will install the system with all requirements.',
           mbInformation, MB_OK);
  end;
end;
