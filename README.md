# نظام الأرشفة الإلكترونية للكتب الرسمية

نظام شامل لإدارة وأرشفة الكتب والمراسلات الرسمية مطور بلغة Python باستخدام إطار عمل Flask مع واجهة مستخدم حديثة باستخدام Bootstrap.

## المميزات الرئيسية

### 🧩 المتطلبات الوظيفية
- ✅ إدخال الكتب الرسمية (وارد/صادر/داخلي)
- ✅ تصنيف الكتب حسب النوع والقسم والأولوية
- ✅ نظام الترقيم والتسلسل التلقائي
- ✅ البحث المتقدم بمعايير متعددة
- ✅ تتبع حركة الكتب والتحويل الإلكتروني
- ✅ نظام صلاحيات المستخدمين المتدرج
- ✅ رفع وإدارة المرفقات
- ✅ إصدار التقارير والإحصائيات
- ✅ الأرشفة حسب السنوات والتصنيفات

### 🎨 واجهة المستخدم
- واجهة عربية متجاوبة باستخدام Bootstrap 5
- تصميم حديث وسهل الاستخدام
- دعم كامل للغة العربية (RTL)
- رموز Font Awesome للتوضيح
- ألوان متدرجة جذابة

### 🔐 نظام الصلاحيات
- **مدير النظام (Admin)**: صلاحيات كاملة
- **مدير (Manager)**: إدارة الكتب والتقارير
- **مستخدم (User)**: إضافة وتعديل الكتب
- **مشاهد (Viewer)**: عرض الكتب فقط

## متطلبات التشغيل

- Python 3.7 أو أحدث
- Flask 2.3+
- SQLite (مدمج مع Python)

## التثبيت والتشغيل

### 1. تحميل المشروع
```bash
git clone [repository-url]
cd safaa
```

### 2. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 3. إعداد قاعدة البيانات والبيانات الأولية
```bash
python init_data.py
```

### 4. تشغيل النظام
```bash
python app.py
```

### 5. فتح النظام في المتصفح
افتح المتصفح وانتقل إلى: `http://localhost:5000`

## المستخدمين الافتراضيين

| اسم المستخدم | كلمة المرور | الدور | الوصف |
|---------------|-------------|-------|--------|
| admin | admin123 | مدير النظام | صلاحيات كاملة |
| manager | manager123 | مدير | إدارة الكتب والتقارير |
| user | user123 | مستخدم | إضافة وتعديل الكتب |

## هيكل المشروع

```
safaa/
├── app.py                 # الملف الرئيسي للتطبيق
├── models.py              # نماذج قاعدة البيانات
├── forms.py               # نماذج الويب
├── init_data.py           # البيانات الأولية
├── requirements.txt       # متطلبات Python
├── uploads/               # مجلد المرفقات
├── templates/             # قوالب HTML
│   ├── base.html         # القالب الأساسي
│   ├── dashboard.html    # لوحة التحكم
│   ├── auth/             # قوالب المصادقة
│   ├── documents/        # قوالب الكتب
│   ├── reports/          # قوالب التقارير
│   ├── users/            # قوالب المستخدمين
│   └── settings/         # قوالب الإعدادات
└── static/               # الملفات الثابتة
```

## الاستخدام

### إضافة كتاب جديد
1. سجل الدخول بحساب له صلاحية الإضافة
2. انقر على "إضافة كتاب جديد" من القائمة الجانبية
3. املأ البيانات المطلوبة
4. أرفق الملفات إن وجدت
5. انقر "حفظ الكتاب"

### البحث في الكتب
1. انتقل إلى "البحث المتقدم"
2. استخدم معايير البحث المختلفة
3. انقر "بحث" لعرض النتائج

### تحويل الكتب
1. افتح الكتاب المراد تحويله
2. انقر "تحويل"
3. اختر المستخدم المراد التحويل إليه
4. أضف ملاحظات إن أردت
5. انقر "تحويل"

### إنشاء التقارير
1. انتقل إلى "التقارير"
2. اختر نوع التقرير والفترة الزمنية
3. انقر "إنشاء التقرير"
4. يمكن تصدير التقرير بصيغة PDF أو Excel

## الميزات التقنية

- **قاعدة البيانات**: SQLite مع إمكانية التطوير لـ PostgreSQL/MySQL
- **الأمان**: تشفير كلمات المرور، حماية CSRF، سجل العمليات
- **الأداء**: فهرسة قاعدة البيانات، تصفح الصفحات
- **التوافق**: يعمل على Windows, Linux, macOS
- **النسخ الاحتياطي**: إمكانية إنشاء نسخ احتياطية من قاعدة البيانات

## التخصيص

يمكن تخصيص النظام من خلال:
- تعديل الألوان في ملف `templates/base.html`
- إضافة أقسام جديدة من إعدادات النظام
- إضافة أنواع كتب جديدة
- تعديل صلاحيات المستخدمين

## الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل:
- راجع ملف التوثيق
- تحقق من سجل الأخطاء في وحدة التحكم
- تأكد من تثبيت جميع المتطلبات بشكل صحيح

## الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام والتطوير.

---

**ملاحظة**: هذا النظام مصمم خصيصاً للبيئة الحكومية العراقية ويدعم اللغة العربية بشكل كامل.
