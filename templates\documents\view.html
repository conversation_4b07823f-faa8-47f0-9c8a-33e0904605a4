{% extends "base.html" %}

{% block title %}{{ document.get_full_number() }} - نظام الأرشفة الإلكترونية{% endblock %}

{% block content %}
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1><i class="fas fa-file-alt me-2"></i>{{ document.get_full_number() }}</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('dashboard') }}">لوحة التحكم</a></li>
                    <li class="breadcrumb-item"><a href="{{ url_for('documents') }}">الكتب</a></li>
                    <li class="breadcrumb-item active">{{ document.get_full_number() }}</li>
                </ol>
            </nav>
        </div>
        <div class="btn-group" role="group">
            {% if current_user.has_permission('update') %}
            <a href="{{ url_for('edit_document', id=document.id) }}" class="btn btn-outline-primary">
                <i class="fas fa-edit me-1"></i>
                تعديل
            </a>
            {% endif %}
            {% if current_user.has_permission('create') %}
            <button type="button" class="btn btn-outline-info" onclick="transferDocument({{ document.id }})">
                <i class="fas fa-share me-1"></i>
                تحويل
            </button>
            {% endif %}
            <button type="button" class="btn btn-outline-secondary" onclick="window.print()">
                <i class="fas fa-print me-1"></i>
                طباعة
            </button>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <!-- معلومات الكتاب الأساسية -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات الكتاب
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td class="fw-bold">رقم الكتاب:</td>
                                <td>{{ document.get_full_number() }}</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">تاريخ الكتاب:</td>
                                <td>{{ document.document_date.strftime('%Y-%m-%d') }}</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">نوع الكتاب:</td>
                                <td>
                                    {% if document.document_type == 'incoming' %}
                                        <span class="badge bg-success">وارد</span>
                                    {% elif document.document_type == 'outgoing' %}
                                        <span class="badge bg-info">صادر</span>
                                    {% else %}
                                        <span class="badge bg-warning">داخلي</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-bold">الأولوية:</td>
                                <td>
                                    {% if document.priority == 'urgent' %}
                                        <span class="badge bg-danger">عاجل</span>
                                    {% elif document.priority == 'high' %}
                                        <span class="badge bg-warning">عالي</span>
                                    {% elif document.priority == 'low' %}
                                        <span class="badge bg-secondary">منخفض</span>
                                    {% else %}
                                        <span class="badge bg-light text-dark">عادي</span>
                                    {% endif %}
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td class="fw-bold">درجة السرية:</td>
                                <td>
                                    {% if document.classification == 'top_secret' %}
                                        <span class="badge bg-danger">سري للغاية</span>
                                    {% elif document.classification == 'secret' %}
                                        <span class="badge bg-warning">سري جداً</span>
                                    {% elif document.classification == 'confidential' %}
                                        <span class="badge bg-info">سري</span>
                                    {% else %}
                                        <span class="badge bg-success">عادي</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% if document.sender %}
                            <tr>
                                <td class="fw-bold">الجهة المرسلة:</td>
                                <td>{{ document.sender }}</td>
                            </tr>
                            {% endif %}
                            {% if document.receiver %}
                            <tr>
                                <td class="fw-bold">الجهة المستقبلة:</td>
                                <td>{{ document.receiver }}</td>
                            </tr>
                            {% endif %}
                            <tr>
                                <td class="fw-bold">أنشئ بواسطة:</td>
                                <td>{{ document.creator.full_name }}</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">تاريخ الإنشاء:</td>
                                <td>{{ document.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- موضوع ومحتوى الكتاب -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-file-text me-2"></i>
                    الموضوع والمحتوى
                </h5>
            </div>
            <div class="card-body">
                <h6 class="fw-bold mb-3">الموضوع:</h6>
                <p class="lead">{{ document.subject }}</p>
                
                {% if document.content %}
                <h6 class="fw-bold mb-3">المحتوى:</h6>
                <div class="border rounded p-3 bg-light">
                    {{ document.content|nl2br }}
                </div>
                {% endif %}
            </div>
        </div>
        
        <!-- المرفقات -->
        {% if document.has_attachments %}
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-paperclip me-2"></i>
                    المرفقات ({{ document.attachments_count }})
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    {% for attachment in document.attachments %}
                    <div class="col-md-6 mb-3">
                        <div class="card border">
                            <div class="card-body p-3">
                                <div class="d-flex align-items-center">
                                    <div class="me-3">
                                        {% if attachment.file_type.startswith('image/') %}
                                            <i class="fas fa-image fa-2x text-primary"></i>
                                        {% elif 'pdf' in attachment.file_type %}
                                            <i class="fas fa-file-pdf fa-2x text-danger"></i>
                                        {% elif 'word' in attachment.file_type or 'document' in attachment.file_type %}
                                            <i class="fas fa-file-word fa-2x text-primary"></i>
                                        {% else %}
                                            <i class="fas fa-file fa-2x text-secondary"></i>
                                        {% endif %}
                                    </div>
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1">{{ attachment.original_filename }}</h6>
                                        <small class="text-muted">
                                            {{ (attachment.file_size / 1024 / 1024)|round(2) }} ميجابايت
                                            • {{ attachment.uploaded_at.strftime('%Y-%m-%d') }}
                                        </small>
                                    </div>
                                    <div>
                                        <a href="{{ url_for('download_attachment', id=attachment.id) }}" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-download"></i>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
        {% endif %}
    </div>
    
    <div class="col-md-4">
        <!-- حركة الكتاب -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-route me-2"></i>
                    حركة الكتاب
                </h5>
            </div>
            <div class="card-body">
                {% if movements %}
                <div class="timeline">
                    {% for movement in movements %}
                    <div class="timeline-item mb-3">
                        <div class="d-flex">
                            <div class="timeline-marker me-3">
                                {% if movement.movement_type == 'forward' %}
                                    <i class="fas fa-arrow-left text-primary"></i>
                                {% elif movement.movement_type == 'return' %}
                                    <i class="fas fa-undo text-warning"></i>
                                {% else %}
                                    <i class="fas fa-copy text-info"></i>
                                {% endif %}
                            </div>
                            <div class="timeline-content">
                                <div class="fw-bold">
                                    {% if movement.movement_type == 'forward' %}
                                        تحويل إلى
                                    {% elif movement.movement_type == 'return' %}
                                        إرجاع من
                                    {% else %}
                                        نسخة إلى
                                    {% endif %}
                                    {{ movement.to_user }}
                                </div>
                                {% if movement.from_user %}
                                <small class="text-muted">من: {{ movement.from_user }}</small><br>
                                {% endif %}
                                <small class="text-muted">{{ movement.created_at.strftime('%Y-%m-%d %H:%M') }}</small>
                                {% if movement.notes %}
                                <div class="mt-2">
                                    <small class="text-muted">{{ movement.notes }}</small>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center py-3">
                    <i class="fas fa-info-circle fa-2x text-muted mb-2"></i>
                    <p class="text-muted mb-0">لا توجد حركة للكتاب</p>
                </div>
                {% endif %}
            </div>
        </div>
        
        <!-- إحصائيات سريعة -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-pie me-2"></i>
                    معلومات إضافية
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <h4 class="text-primary">{{ movements|length }}</h4>
                            <small class="text-muted">حركات</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h4 class="text-success">{{ document.attachments_count }}</h4>
                        <small class="text-muted">مرفقات</small>
                    </div>
                </div>
                
                <hr>
                
                <div class="d-grid gap-2">
                    <a href="{{ url_for('documents') }}" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-list me-1"></i>
                        العودة للقائمة
                    </a>
                    <a href="{{ url_for('search') }}?document_number={{ document.document_number }}" class="btn btn-outline-info btn-sm">
                        <i class="fas fa-search me-1"></i>
                        كتب مشابهة
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal تحويل الكتاب -->
<div class="modal fade" id="transferModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تحويل الكتاب {{ document.get_full_number() }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="transferForm">
                    <input type="hidden" id="documentId" name="document_id" value="{{ document.id }}">
                    <div class="mb-3">
                        <label for="toUser" class="form-label">تحويل إلى</label>
                        <select class="form-select" id="toUser" name="to_user_id" required>
                            <option value="">اختر المستخدم...</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="movementType" class="form-label">نوع التحويل</label>
                        <select class="form-select" id="movementType" name="movement_type" required>
                            <option value="forward">تحويل</option>
                            <option value="copy">نسخة</option>
                            <option value="return">إرجاع</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="notes" class="form-label">ملاحظات</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="submitTransfer()">تحويل</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.timeline-item {
    position: relative;
}

.timeline-marker {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background-color: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid #dee2e6;
}

.timeline-content {
    flex: 1;
    padding-top: 5px;
}

@media print {
    .btn-group, .modal, .page-header .btn-group {
        display: none !important;
    }
}
</style>
{% endblock %}

{% block extra_js %}
<script>
function transferDocument(documentId) {
    $('#transferModal').modal('show');
    
    // تحميل قائمة المستخدمين
    $.get('/api/users', function(users) {
        var select = $('#toUser');
        select.empty().append('<option value="">اختر المستخدم...</option>');
        users.forEach(function(user) {
            select.append('<option value="' + user.id + '">' + user.full_name + ' - ' + user.department + '</option>');
        });
    });
}

function submitTransfer() {
    var formData = {
        document_id: $('#documentId').val(),
        to_user_id: $('#toUser').val(),
        movement_type: $('#movementType').val(),
        notes: $('#notes').val()
    };
    
    $.post('/api/transfer-document', formData)
        .done(function(response) {
            $('#transferModal').modal('hide');
            location.reload();
        })
        .fail(function(xhr) {
            alert('حدث خطأ أثناء تحويل الكتاب: ' + xhr.responseJSON.message);
        });
}
</script>
{% endblock %}
