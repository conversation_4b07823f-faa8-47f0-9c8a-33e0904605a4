#!/usr/bin/env python3
"""
ملف تشغيل نظام الأرشفة الإلكترونية
"""

import os
import sys

def check_requirements():
    """التحقق من المتطلبات الأساسية"""
    try:
        import flask
        import flask_sqlalchemy
        import flask_login
        import flask_wtf
        print("✅ جميع المتطلبات متوفرة")
        return True
    except ImportError as e:
        print(f"❌ مكتبة مفقودة: {e}")
        print("يرجى تثبيت المتطلبات باستخدام:")
        print("pip install flask flask-sqlalchemy flask-login flask-wtf")
        return False

def setup_database():
    """إعداد قاعدة البيانات والبيانات الأولية"""
    try:
        from app import app
        from models import db, User, Department, DocumentType, SystemSettings
        from datetime import datetime
        
        with app.app_context():
            # إنشاء الجداول
            db.create_all()
            print("✅ تم إنشاء قاعدة البيانات")
            
            # إنشاء المستخدم الافتراضي إذا لم يكن موجوداً
            if not User.query.filter_by(username='admin').first():
                admin_user = User(
                    username='admin',
                    email='<EMAIL>',
                    full_name='مدير النظام',
                    role='admin',
                    department='إدارة النظام',
                    is_active=True
                )
                admin_user.set_password('admin123')
                db.session.add(admin_user)
                
                # إضافة مستخدمين آخرين
                manager_user = User(
                    username='manager',
                    email='<EMAIL>',
                    full_name='مدير الأرشيف',
                    role='manager',
                    department='قسم الأرشيف',
                    is_active=True
                )
                manager_user.set_password('manager123')
                db.session.add(manager_user)
                
                user_user = User(
                    username='user',
                    email='<EMAIL>',
                    full_name='موظف الأرشيف',
                    role='user',
                    department='قسم الكتب الواردة',
                    is_active=True
                )
                user_user.set_password('user123')
                db.session.add(user_user)
                
                db.session.commit()
                print("✅ تم إنشاء المستخدمين الافتراضيين")
            
            # إنشاء الأقسام الافتراضية
            departments_data = [
                {'name': 'قسم الكتب الواردة', 'code': 'INC'},
                {'name': 'قسم الكتب الصادرة', 'code': 'OUT'},
                {'name': 'قسم الأرشيف', 'code': 'ARC'},
                {'name': 'الإدارة العامة', 'code': 'GEN'}
            ]
            
            for dept_data in departments_data:
                if not Department.query.filter_by(code=dept_data['code']).first():
                    department = Department(
                        name=dept_data['name'],
                        code=dept_data['code'],
                        description=f"وصف {dept_data['name']}",
                        is_active=True
                    )
                    db.session.add(department)
            
            # إنشاء أنواع الكتب الافتراضية
            document_types_data = [
                {'name': 'كتاب رسمي', 'code': 'OFF'},
                {'name': 'تعميم', 'code': 'CIR'},
                {'name': 'قرار إداري', 'code': 'DEC'},
                {'name': 'تقرير', 'code': 'REP'}
            ]
            
            for type_data in document_types_data:
                if not DocumentType.query.filter_by(code=type_data['code']).first():
                    doc_type = DocumentType(
                        name=type_data['name'],
                        code=type_data['code'],
                        description=f"وصف {type_data['name']}",
                        is_active=True
                    )
                    db.session.add(doc_type)
            
            db.session.commit()
            print("✅ تم إنشاء البيانات الأولية")
            
        return True
    except Exception as e:
        print(f"❌ خطأ في إعداد قاعدة البيانات: {e}")
        return False

def run_app():
    """تشغيل التطبيق"""
    try:
        from app import app
        print("\n" + "="*50)
        print("🚀 بدء تشغيل نظام الأرشفة الإلكترونية")
        print("="*50)
        print("📍 الرابط: http://localhost:5000")
        print("👤 المستخدمين الافتراضيين:")
        print("   - admin / admin123 (مدير النظام)")
        print("   - manager / manager123 (مدير الأرشيف)")
        print("   - user / user123 (موظف عادي)")
        print("⏹️  للإيقاف: اضغط Ctrl+C")
        print("="*50)
        
        app.run(debug=True, host='0.0.0.0', port=5000)
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🗂️  نظام الأرشفة الإلكترونية للكتب الرسمية")
    print("="*50)
    
    # التحقق من المتطلبات
    if not check_requirements():
        sys.exit(1)
    
    # إعداد قاعدة البيانات
    if not setup_database():
        sys.exit(1)
    
    # تشغيل التطبيق
    run_app()

if __name__ == '__main__':
    main()
