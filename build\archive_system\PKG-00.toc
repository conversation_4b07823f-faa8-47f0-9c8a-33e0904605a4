('C:\\Users\\<USER>\\OneDrive\\Desktop\\test '
 '111\\safaa\\build\\archive_system\\ArchiveSystem.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'C:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\build\\archive_system\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'C:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\build\\archive_system\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'C:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\build\\archive_system\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'C:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\build\\archive_system\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'C:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\build\\archive_system\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'C:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\build\\archive_system\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'C:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'C:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'C:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'C:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('app',
   'C:\\Users\\<USER>\\OneDrive\\Desktop\\test 111\\safaa\\app.py',
   'PYSOURCE')],
 'python313.dll',
 True,
 False,
 False,
 [],
 None,
 None,
 None)
