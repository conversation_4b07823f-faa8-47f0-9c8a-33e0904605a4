#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام الأرشفة الإلكترونية - مشغل التطبيق
Electronic Archive System - Application Launcher

هذا الملف مسؤول عن بدء تشغيل النظام وإعداد البيئة المطلوبة
"""

import os
import sys
import webbrowser
import time
import threading
import socket
from pathlib import Path

def get_resource_path(relative_path):
    """الحصول على المسار الصحيح للملفات في البيئة المجمعة"""
    try:
        # PyInstaller creates a temp folder and stores path in _MEIPASS
        base_path = sys._MEIPASS
    except Exception:
        base_path = os.path.abspath(".")
    
    return os.path.join(base_path, relative_path)

def setup_directories():
    """إنشاء المجلدات المطلوبة"""
    directories = [
        'instance',
        'uploads', 
        'backups',
        'static',
        'templates'
    ]
    
    for directory in directories:
        dir_path = Path(directory)
        if not dir_path.exists():
            dir_path.mkdir(parents=True, exist_ok=True)
            print(f"تم إنشاء المجلد: {directory}")

def check_database():
    """التحقق من وجود قاعدة البيانات وإنشاؤها إذا لزم الأمر"""
    db_path = Path('instance/archive.db')
    
    if not db_path.exists():
        print("إنشاء قاعدة البيانات...")
        try:
            # استيراد التطبيق وإنشاء قاعدة البيانات
            from app import app, db
            with app.app_context():
                db.create_all()
                print("تم إنشاء قاعدة البيانات بنجاح")
                
                # إنشاء المستخدم الافتراضي
                from models import User
                admin_user = User.query.filter_by(username='admin').first()
                if not admin_user:
                    admin_user = User(
                        username='admin',
                        email='<EMAIL>',
                        full_name='مدير النظام',
                        role='admin',
                        department='إدارة النظام'
                    )
                    admin_user.set_password('admin123')
                    db.session.add(admin_user)
                    db.session.commit()
                    print("تم إنشاء المستخدم الافتراضي: admin / admin123")
                    
        except Exception as e:
            print(f"خطأ في إنشاء قاعدة البيانات: {e}")
            return False
    else:
        print("قاعدة البيانات موجودة")
    
    return True

def is_port_available(port):
    """التحقق من توفر المنفذ"""
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        try:
            s.bind(('localhost', port))
            return True
        except socket.error:
            return False

def find_available_port(start_port=5000):
    """البحث عن منفذ متاح"""
    port = start_port
    while port < start_port + 100:
        if is_port_available(port):
            return port
        port += 1
    return None

def open_browser(url, delay=2):
    """فتح المتصفح بعد تأخير"""
    time.sleep(delay)
    try:
        webbrowser.open(url)
        print(f"تم فتح المتصفح: {url}")
    except Exception as e:
        print(f"لا يمكن فتح المتصفح تلقائياً: {e}")
        print(f"يرجى فتح المتصفح يدوياً والذهاب إلى: {url}")

def main():
    """الدالة الرئيسية لبدء التطبيق"""
    print("=" * 60)
    print("🏛️  نظام الأرشفة الإلكترونية")
    print("📋  Electronic Archive System")
    print("=" * 60)
    
    # إعداد المجلدات
    print("\n📁 إعداد المجلدات...")
    setup_directories()
    
    # التحقق من قاعدة البيانات
    print("\n🗄️  التحقق من قاعدة البيانات...")
    if not check_database():
        print("❌ فشل في إعداد قاعدة البيانات")
        input("اضغط Enter للخروج...")
        return
    
    # البحث عن منفذ متاح
    print("\n🔍 البحث عن منفذ متاح...")
    port = find_available_port(5000)
    if not port:
        print("❌ لا يمكن العثور على منفذ متاح")
        input("اضغط Enter للخروج...")
        return
    
    print(f"✅ سيتم استخدام المنفذ: {port}")
    
    # إعداد متغيرات البيئة
    os.environ['FLASK_ENV'] = 'production'
    os.environ['FLASK_DEBUG'] = '0'
    
    # بدء الخادم
    print(f"\n🚀 بدء تشغيل الخادم على المنفذ {port}...")
    print("⏳ يرجى الانتظار...")
    
    # فتح المتصفح في خيط منفصل
    url = f"http://localhost:{port}"
    browser_thread = threading.Thread(target=open_browser, args=(url, 3))
    browser_thread.daemon = True
    browser_thread.start()
    
    try:
        # استيراد وتشغيل التطبيق
        from app import app
        
        print(f"\n✅ الخادم يعمل على: {url}")
        print("📌 معلومات تسجيل الدخول الافتراضية:")
        print("   👤 اسم المستخدم: admin")
        print("   🔑 كلمة المرور: admin123")
        print("\n⚠️  لإيقاف الخادم: اضغط Ctrl+C")
        print("=" * 60)
        
        # تشغيل التطبيق
        app.run(
            host='0.0.0.0',
            port=port,
            debug=False,
            use_reloader=False,
            threaded=True
        )
        
    except KeyboardInterrupt:
        print("\n\n🛑 تم إيقاف الخادم بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل الخادم: {e}")
        input("اضغط Enter للخروج...")

if __name__ == '__main__':
    main()
