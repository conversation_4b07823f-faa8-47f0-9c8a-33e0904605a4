from flask_wtf import FlaskForm
from flask_wtf.file import FileField, FileAllowed, FileRequired
from wtforms import StringField, TextAreaField, SelectField, DateField, IntegerField, BooleanField, PasswordField, HiddenField
from wtforms.validators import DataRequired, Length, Email, Optional, NumberRange, ValidationError
from wtforms.widgets import TextArea
from datetime import date
from models import User, Department, DocumentType

class LoginForm(FlaskForm):
    """نموذج تسجيل الدخول"""
    username = StringField('اسم المستخدم', validators=[DataRequired(), Length(min=3, max=80)])
    password = PasswordField('كلمة المرور', validators=[DataRequired()])

class UserForm(FlaskForm):
    """نموذج إضافة/تعديل المستخدمين"""
    username = StringField('اسم المستخدم', validators=[DataRequired(), Length(min=3, max=80)])
    full_name = StringField('الاسم الكامل', validators=[DataRequired(), Length(max=100)])
    password = PasswordField('كلمة المرور', validators=[Optional(), Length(min=6)])
    role = SelectField('الدور', choices=[
        ('admin', 'مدير النظام'),
        ('manager', 'مدير'),
        ('user', 'مستخدم'),
        ('viewer', 'مشاهد فقط')
    ], validators=[DataRequired()])
    department = StringField('القسم', validators=[DataRequired(), Length(max=100)])
    is_active = BooleanField('نشط')

    def __init__(self, user=None, *args, **kwargs):
        super(UserForm, self).__init__(*args, **kwargs)
        self.user = user

    def validate_username(self, field):
        user = User.query.filter_by(username=field.data).first()
        if user and (not self.user or user.id != self.user.id):
            raise ValidationError('اسم المستخدم موجود بالفعل.')

class DocumentForm(FlaskForm):
    """نموذج إضافة/تعديل الكتب"""
    document_number = StringField('رقم الكتاب', validators=[DataRequired(), Length(max=50)])
    document_date = DateField('تاريخ الكتاب', validators=[DataRequired()], default=date.today)
    subject = StringField('الموضوع', validators=[DataRequired(), Length(max=200)])
    content = TextAreaField('المحتوى', widget=TextArea())
    
    document_type = SelectField('نوع الكتاب', choices=[
        ('incoming', 'وارد'),
        ('outgoing', 'صادر'),
        ('internal', 'داخلي')
    ], validators=[DataRequired()])
    
    document_type_id = SelectField('تصنيف الكتاب', coerce=int, validators=[Optional()])
    department_id = SelectField('القسم', coerce=int, validators=[Optional()])
    
    sender = StringField('الجهة المرسلة', validators=[Optional(), Length(max=200)])
    receiver = StringField('الجهة المستقبلة', validators=[Optional(), Length(max=200)])
    
    classification = SelectField('درجة السرية', choices=[
        ('normal', 'عادي'),
        ('confidential', 'سري'),
        ('secret', 'سري جداً'),
        ('top_secret', 'سري للغاية')
    ], default='normal')
    
    priority = SelectField('الأولوية', choices=[
        ('low', 'منخفضة'),
        ('normal', 'عادية'),
        ('high', 'عالية'),
        ('urgent', 'عاجل')
    ], default='normal')
    
    attachments = FileField('المرفقات', validators=[
        FileAllowed(['pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png', 'gif'], 'أنواع الملفات المسموحة: PDF, DOC, DOCX, JPG, PNG, GIF')
    ])

    def __init__(self, *args, **kwargs):
        super(DocumentForm, self).__init__(*args, **kwargs)
        # تحديث خيارات الأقسام وأنواع الكتب
        self.department_id.choices = [(0, 'اختر القسم')] + [(d.id, d.name) for d in Department.query.filter_by(is_active=True).all()]
        self.document_type_id.choices = [(0, 'اختر التصنيف')] + [(dt.id, dt.name) for dt in DocumentType.query.filter_by(is_active=True).all()]

class SearchForm(FlaskForm):
    """نموذج البحث المتقدم"""
    search_text = StringField('نص البحث')
    document_number = StringField('رقم الكتاب')
    date_from = DateField('من تاريخ', validators=[Optional()])
    date_to = DateField('إلى تاريخ', validators=[Optional()])
    document_type = SelectField('نوع الكتاب', choices=[
        ('', 'جميع الأنواع'),
        ('incoming', 'وارد'),
        ('outgoing', 'صادر'),
        ('internal', 'داخلي')
    ])
    department_id = SelectField('القسم', coerce=int, validators=[Optional()])
    classification = SelectField('درجة السرية', choices=[
        ('', 'جميع الدرجات'),
        ('normal', 'عادي'),
        ('confidential', 'سري'),
        ('secret', 'سري جداً'),
        ('top_secret', 'سري للغاية')
    ])
    priority = SelectField('الأولوية', choices=[
        ('', 'جميع الأولويات'),
        ('low', 'منخفضة'),
        ('normal', 'عادية'),
        ('high', 'عالية'),
        ('urgent', 'عاجل')
    ])
    sender = StringField('الجهة المرسلة')
    receiver = StringField('الجهة المستقبلة')

    def __init__(self, *args, **kwargs):
        super(SearchForm, self).__init__(*args, **kwargs)
        self.department_id.choices = [(0, 'جميع الأقسام')] + [(d.id, d.name) for d in Department.query.filter_by(is_active=True).all()]

class DocumentMovementForm(FlaskForm):
    """نموذج تحويل الكتب"""
    to_user_id = SelectField('إلى المستخدم', coerce=int, validators=[DataRequired()])
    movement_type = SelectField('نوع التحويل', choices=[
        ('forward', 'تحويل'),
        ('return', 'إرجاع'),
        ('copy', 'نسخة')
    ], validators=[DataRequired()])
    notes = TextAreaField('ملاحظات')

    def __init__(self, *args, **kwargs):
        super(DocumentMovementForm, self).__init__(*args, **kwargs)
        self.to_user_id.choices = [(u.id, f"{u.full_name} - {u.department}") for u in User.query.filter_by(is_active=True).all()]

class DepartmentForm(FlaskForm):
    """نموذج إضافة/تعديل الأقسام"""
    name = StringField('اسم القسم', validators=[DataRequired(), Length(max=100)])
    code = StringField('رمز القسم', validators=[DataRequired(), Length(max=10)])
    description = TextAreaField('الوصف')
    is_active = BooleanField('نشط', default=True)

class DocumentTypeForm(FlaskForm):
    """نموذج إضافة/تعديل أنواع الكتب"""
    name = StringField('اسم النوع', validators=[DataRequired(), Length(max=50)])
    code = StringField('رمز النوع', validators=[DataRequired(), Length(max=10)])
    description = TextAreaField('الوصف')
    is_active = BooleanField('نشط', default=True)

class ReportForm(FlaskForm):
    """نموذج التقارير"""
    report_type = SelectField('نوع التقرير', choices=[
        ('documents_summary', 'ملخص الكتب'),
        ('documents_by_type', 'الكتب حسب النوع'),
        ('documents_by_department', 'الكتب حسب القسم'),
        ('documents_by_date', 'الكتب حسب التاريخ'),
        ('user_activity', 'نشاط المستخدمين')
    ], validators=[DataRequired()])
    
    date_from = DateField('من تاريخ', validators=[DataRequired()])
    date_to = DateField('إلى تاريخ', validators=[DataRequired()])
    department_id = SelectField('القسم', coerce=int, validators=[Optional()])
    document_type = SelectField('نوع الكتاب', choices=[
        ('', 'جميع الأنواع'),
        ('incoming', 'وارد'),
        ('outgoing', 'صادر'),
        ('internal', 'داخلي')
    ])

    def __init__(self, *args, **kwargs):
        super(ReportForm, self).__init__(*args, **kwargs)
        self.department_id.choices = [(0, 'جميع الأقسام')] + [(d.id, d.name) for d in Department.query.filter_by(is_active=True).all()]
