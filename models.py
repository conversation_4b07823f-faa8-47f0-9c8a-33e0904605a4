from flask_sqlalchemy import SQLAlchemy
from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime
import json

db = SQLAlchemy()

class User(UserMixin, db.Model):
    """نموذج المستخدمين"""
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(120), nullable=False)
    full_name = db.Column(db.String(100), nullable=False)
    role = db.Column(db.String(20), nullable=False, default='user')  # admin, manager, user, viewer
    department = db.Column(db.String(100), nullable=False)
    is_active = db.Column(db.<PERSON>, default=True)
    created_at = db.Column(db.DateTime, default=datetime.now)
    last_login = db.Column(db.DateTime)
    
    # العلاقات
    created_documents = db.relationship('Document', foreign_keys='Document.created_by', backref='creator', lazy='dynamic')
    assigned_documents = db.relationship('DocumentMovement', foreign_keys='DocumentMovement.to_user_id', backref='assigned_user', lazy='dynamic')

    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)
    
    def has_permission(self, permission):
        """التحقق من الصلاحيات"""
        permissions = {
            'admin': ['create', 'read', 'update', 'delete', 'manage_users', 'view_reports'],
            'manager': ['create', 'read', 'update', 'delete', 'view_reports'],
            'user': ['create', 'read', 'update', 'delete'],
            'viewer': ['read']
        }
        return permission in permissions.get(self.role, [])

class Department(db.Model):
    """نموذج الأقسام"""
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, unique=True)
    code = db.Column(db.String(10), nullable=False, unique=True)
    description = db.Column(db.Text)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.now)
    
    # العلاقات
    documents = db.relationship('Document', backref='department_ref', lazy='dynamic')

class DocumentType(db.Model):
    """نموذج أنواع الكتب"""
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(50), nullable=False, unique=True)
    code = db.Column(db.String(10), nullable=False, unique=True)
    description = db.Column(db.Text)
    is_active = db.Column(db.Boolean, default=True)
    
    # العلاقات
    documents = db.relationship('Document', backref='document_type_ref', lazy='dynamic')

class Document(db.Model):
    """نموذج الكتب الرسمية"""
    id = db.Column(db.Integer, primary_key=True)
    
    # معلومات أساسية
    document_number = db.Column(db.String(50), nullable=False)
    document_date = db.Column(db.Date, nullable=False)
    subject = db.Column(db.String(200), nullable=False)
    content = db.Column(db.Text)
    
    # التصنيف
    document_type = db.Column(db.String(20), nullable=False)  # incoming, outgoing, internal
    document_type_id = db.Column(db.Integer, db.ForeignKey('document_type.id'))
    department_id = db.Column(db.Integer, db.ForeignKey('department.id'))
    
    # الجهات
    sender = db.Column(db.String(200))  # الجهة المرسلة
    receiver = db.Column(db.String(200))  # الجهة المستقبلة
    
    # التصنيف والأولوية
    classification = db.Column(db.String(20), default='normal')  # normal, confidential, secret, top_secret
    priority = db.Column(db.String(20), default='normal')  # low, normal, high, urgent
    
    # الحالة
    status = db.Column(db.String(20), default='active')  # active, archived, deleted
    
    # المرفقات
    has_attachments = db.Column(db.Boolean, default=False)
    attachments_count = db.Column(db.Integer, default=0)
    
    # معلومات النظام
    created_by = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)
    
    # العلاقات
    attachments = db.relationship('DocumentAttachment', backref='document', lazy='dynamic', cascade='all, delete-orphan')
    movements = db.relationship('DocumentMovement', backref='document', lazy='dynamic', cascade='all, delete-orphan')
    
    def get_current_location(self):
        """الحصول على الموقع الحالي للكتاب"""
        latest_movement = self.movements.order_by(DocumentMovement.created_at.desc()).first()
        return latest_movement.to_user if latest_movement else self.creator.full_name
    
    def get_full_number(self):
        """الحصول على الرقم الكامل للكتاب"""
        year = self.document_date.year
        return f"{self.document_number}/{year}"

class DocumentAttachment(db.Model):
    """نموذج مرفقات الكتب"""
    id = db.Column(db.Integer, primary_key=True)
    document_id = db.Column(db.Integer, db.ForeignKey('document.id'), nullable=False)
    filename = db.Column(db.String(255), nullable=False)
    original_filename = db.Column(db.String(255), nullable=False)
    file_size = db.Column(db.Integer)
    file_type = db.Column(db.String(50))
    file_path = db.Column(db.String(500), nullable=False)
    uploaded_by = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    uploaded_at = db.Column(db.DateTime, default=datetime.now)
    
    # العلاقات
    uploader = db.relationship('User', backref='uploaded_files')

class DocumentMovement(db.Model):
    """نموذج حركة الكتب"""
    id = db.Column(db.Integer, primary_key=True)
    document_id = db.Column(db.Integer, db.ForeignKey('document.id'), nullable=False)
    from_user = db.Column(db.String(100))  # المرسل
    to_user = db.Column(db.String(100), nullable=False)  # المستقبل
    to_user_id = db.Column(db.Integer, db.ForeignKey('user.id'))
    movement_type = db.Column(db.String(20), nullable=False)  # forward, return, copy
    notes = db.Column(db.Text)
    is_read = db.Column(db.Boolean, default=False)
    read_at = db.Column(db.DateTime)
    created_by = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.now)
    
    # العلاقات
    creator = db.relationship('User', foreign_keys=[created_by], backref='created_movements')

class SystemSettings(db.Model):
    """نموذج إعدادات النظام"""
    id = db.Column(db.Integer, primary_key=True)
    key = db.Column(db.String(100), unique=True, nullable=False)
    value = db.Column(db.Text)
    description = db.Column(db.Text)
    updated_by = db.Column(db.Integer, db.ForeignKey('user.id'))
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)
    
    @staticmethod
    def get_setting(key, default=None):
        setting = SystemSettings.query.filter_by(key=key).first()
        return setting.value if setting else default
    
    @staticmethod
    def set_setting(key, value, description=None):
        setting = SystemSettings.query.filter_by(key=key).first()
        if setting:
            setting.value = value
            if description:
                setting.description = description
        else:
            setting = SystemSettings(key=key, value=value, description=description)
            db.session.add(setting)
        db.session.commit()

class AuditLog(db.Model):
    """نموذج سجل العمليات"""
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    action = db.Column(db.String(50), nullable=False)  # create, update, delete, login, logout
    table_name = db.Column(db.String(50))
    record_id = db.Column(db.Integer)
    old_values = db.Column(db.Text)  # JSON
    new_values = db.Column(db.Text)  # JSON
    ip_address = db.Column(db.String(45))
    user_agent = db.Column(db.String(500))
    created_at = db.Column(db.DateTime, default=datetime.now)
    
    # العلاقات
    user = db.relationship('User', backref='audit_logs')
    
    @staticmethod
    def log_action(user_id, action, table_name=None, record_id=None, old_values=None, new_values=None, ip_address=None, user_agent=None):
        log = AuditLog(
            user_id=user_id,
            action=action,
            table_name=table_name,
            record_id=record_id,
            old_values=json.dumps(old_values) if old_values else None,
            new_values=json.dumps(new_values) if new_values else None,
            ip_address=ip_address,
            user_agent=user_agent
        )
        db.session.add(log)
        db.session.commit()
