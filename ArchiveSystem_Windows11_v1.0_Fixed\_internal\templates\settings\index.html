{% extends "base.html" %}

{% block title %}إعدادات النظام - نظام الأرشفة الإلكترونية{% endblock %}

{% block content %}
<div class="page-header">
    <h1><i class="fas fa-cogs me-2"></i>إعدادات النظام</h1>
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ url_for('dashboard') }}">لوحة التحكم</a></li>
            <li class="breadcrumb-item active">إعدادات النظام</li>
        </ol>
    </nav>
</div>

<div class="row">
    <!-- إدارة الأقسام -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-building me-2"></i>
                        إدارة الأقسام
                    </h5>
                    <button type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#addDepartmentModal">
                        <i class="fas fa-plus me-1"></i>
                        إضافة قسم
                    </button>
                </div>
            </div>
            <div class="card-body">
                {% if departments %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>اسم القسم</th>
                                <th>الرمز</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for dept in departments %}
                            <tr>
                                <td>{{ dept.name }}</td>
                                <td><code>{{ dept.code }}</code></td>
                                <td>
                                    {% if dept.is_active %}
                                        <span class="badge bg-success">نشط</span>
                                    {% else %}
                                        <span class="badge bg-secondary">غير نشط</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <button type="button" class="btn btn-outline-primary" onclick="editDepartment({{ dept.id }})">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button type="button" class="btn btn-outline-danger" onclick="deleteDepartment({{ dept.id }})">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-3">
                    <i class="fas fa-building fa-2x text-muted mb-2"></i>
                    <p class="text-muted">لا توجد أقسام مضافة</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- إدارة أنواع الكتب -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-tags me-2"></i>
                        أنواع الكتب
                    </h5>
                    <button type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#addDocumentTypeModal">
                        <i class="fas fa-plus me-1"></i>
                        إضافة نوع
                    </button>
                </div>
            </div>
            <div class="card-body">
                {% if document_types %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>اسم النوع</th>
                                <th>الرمز</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for type in document_types %}
                            <tr>
                                <td>{{ type.name }}</td>
                                <td><code>{{ type.code }}</code></td>
                                <td>
                                    {% if type.is_active %}
                                        <span class="badge bg-success">نشط</span>
                                    {% else %}
                                        <span class="badge bg-secondary">غير نشط</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <button type="button" class="btn btn-outline-primary" onclick="editDocumentType({{ type.id }})">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button type="button" class="btn btn-outline-danger" onclick="deleteDocumentType({{ type.id }})">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-3">
                    <i class="fas fa-tags fa-2x text-muted mb-2"></i>
                    <p class="text-muted">لا توجد أنواع كتب مضافة</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <!-- إعدادات عامة -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-sliders-h me-2"></i>
                    الإعدادات العامة
                </h5>
            </div>
            <div class="card-body">
                <form id="generalSettingsForm">
                    <div class="mb-3">
                        <label for="systemName" class="form-label">اسم النظام</label>
                        <input type="text" class="form-control" id="systemName" value="نظام الأرشفة الإلكترونية">
                    </div>
                    
                    <div class="mb-3">
                        <label for="organizationName" class="form-label">اسم المؤسسة</label>
                        <input type="text" class="form-control" id="organizationName" placeholder="أدخل اسم المؤسسة">
                    </div>
                    
                    <div class="mb-3">
                        <label for="maxFileSize" class="form-label">الحد الأقصى لحجم الملف (ميجابايت)</label>
                        <input type="number" class="form-control" id="maxFileSize" value="16" min="1" max="100">
                    </div>
                    
                    <div class="mb-3">
                        <label for="documentsPerPage" class="form-label">عدد الكتب في الصفحة</label>
                        <select class="form-select" id="documentsPerPage">
                            <option value="10">10</option>
                            <option value="20" selected>20</option>
                            <option value="50">50</option>
                            <option value="100">100</option>
                        </select>
                    </div>
                    
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="enableNotifications" checked>
                        <label class="form-check-label" for="enableNotifications">
                            تفعيل الإشعارات
                        </label>
                    </div>
                    
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="enableAuditLog" checked>
                        <label class="form-check-label" for="enableAuditLog">
                            تفعيل سجل العمليات
                        </label>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>
                        حفظ الإعدادات
                    </button>
                </form>
            </div>
        </div>
    </div>
    
    <!-- النسخ الاحتياطي -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-database me-2"></i>
                    النسخ الاحتياطي
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    آخر نسخة احتياطية: لم يتم إنشاء نسخة احتياطية بعد
                </div>
                
                <div class="d-grid gap-2">
                    <button type="button" class="btn btn-success" onclick="createBackup()">
                        <i class="fas fa-download me-2"></i>
                        إنشاء نسخة احتياطية
                    </button>
                    
                    <button type="button" class="btn btn-warning" onclick="restoreBackup()">
                        <i class="fas fa-upload me-2"></i>
                        استعادة نسخة احتياطية
                    </button>
                    
                    <button type="button" class="btn btn-info" onclick="scheduleBackup()">
                        <i class="fas fa-clock me-2"></i>
                        جدولة النسخ الاحتياطي
                    </button>
                </div>
                
                <hr>
                
                <h6>إحصائيات قاعدة البيانات</h6>
                <div class="row text-center">
                    <div class="col-4">
                        <div class="border rounded p-2">
                            <h5 class="text-primary mb-0">{{ departments|length }}</h5>
                            <small class="text-muted">أقسام</small>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="border rounded p-2">
                            <h5 class="text-success mb-0">{{ document_types|length }}</h5>
                            <small class="text-muted">أنواع</small>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="border rounded p-2">
                            <h5 class="text-info mb-0">0</h5>
                            <small class="text-muted">مستخدمين</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal إضافة قسم -->
<div class="modal fade" id="addDepartmentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة قسم جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="departmentForm">
                    <div class="mb-3">
                        <label for="deptName" class="form-label">اسم القسم</label>
                        <input type="text" class="form-control" id="deptName" required>
                    </div>
                    <div class="mb-3">
                        <label for="deptCode" class="form-label">رمز القسم</label>
                        <input type="text" class="form-control" id="deptCode" required>
                    </div>
                    <div class="mb-3">
                        <label for="deptDescription" class="form-label">الوصف</label>
                        <textarea class="form-control" id="deptDescription" rows="3"></textarea>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="deptActive" checked>
                        <label class="form-check-label" for="deptActive">
                            نشط
                        </label>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" onclick="resetDepartmentForm()">إلغاء</button>
                <button type="button" class="btn btn-primary" id="saveDepartmentBtn" onclick="saveDepartment()">حفظ</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal إضافة نوع كتاب -->
<div class="modal fade" id="addDocumentTypeModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة نوع كتاب جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="documentTypeForm">
                    <div class="mb-3">
                        <label for="typeName" class="form-label">اسم النوع</label>
                        <input type="text" class="form-control" id="typeName" required>
                    </div>
                    <div class="mb-3">
                        <label for="typeCode" class="form-label">رمز النوع</label>
                        <input type="text" class="form-control" id="typeCode" required>
                    </div>
                    <div class="mb-3">
                        <label for="typeDescription" class="form-label">الوصف</label>
                        <textarea class="form-control" id="typeDescription" rows="3"></textarea>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="typeActive" checked>
                        <label class="form-check-label" for="typeActive">
                            نشط
                        </label>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" onclick="resetDocumentTypeForm()">إلغاء</button>
                <button type="button" class="btn btn-primary" id="saveDocumentTypeBtn" onclick="saveDocumentType()">حفظ</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function saveDepartment() {
    var formData = {
        name: $('#deptName').val(),
        code: $('#deptCode').val(),
        description: $('#deptDescription').val(),
        is_active: $('#deptActive').is(':checked')
    };

    $.post('/api/departments', formData)
        .done(function(response) {
            $('#addDepartmentModal').modal('hide');
            resetDepartmentForm();
            location.reload();
        })
        .fail(function(xhr) {
            alert('حدث خطأ: ' + xhr.responseJSON.message);
        });
}

function resetDepartmentForm() {
    $('#departmentForm')[0].reset();
    $('#addDepartmentModal .modal-title').text('إضافة قسم جديد');
    $('#saveDepartmentBtn').attr('onclick', 'saveDepartment()');
}

function saveDocumentType() {
    var formData = {
        name: $('#typeName').val(),
        code: $('#typeCode').val(),
        description: $('#typeDescription').val(),
        is_active: $('#typeActive').is(':checked')
    };

    $.post('/api/document-types', formData)
        .done(function(response) {
            $('#addDocumentTypeModal').modal('hide');
            resetDocumentTypeForm();
            location.reload();
        })
        .fail(function(xhr) {
            alert('حدث خطأ: ' + xhr.responseJSON.message);
        });
}

function resetDocumentTypeForm() {
    $('#documentTypeForm')[0].reset();
    $('#addDocumentTypeModal .modal-title').text('إضافة نوع كتاب جديد');
    $('#saveDocumentTypeBtn').attr('onclick', 'saveDocumentType()');
}

function createBackup() {
    if (confirm('هل تريد إنشاء نسخة احتياطية من قاعدة البيانات؟')) {
        window.location.href = '/api/backup';
    }
}

function restoreBackup() {
    alert('ميزة استعادة النسخة الاحتياطية ستكون متاحة قريباً');
}

function scheduleBackup() {
    alert('ميزة جدولة النسخ الاحتياطي ستكون متاحة قريباً');
}

function editDepartment(deptId) {
    // جلب بيانات القسم
    $.get('/api/departments/' + deptId)
        .done(function(dept) {
            // ملء النموذج ببيانات القسم
            $('#deptName').val(dept.name);
            $('#deptCode').val(dept.code);
            $('#deptDescription').val(dept.description);
            $('#deptActive').prop('checked', dept.is_active);

            // تغيير عنوان النافذة
            $('#addDepartmentModal .modal-title').text('تعديل القسم');

            // تغيير زر الحفظ
            $('#saveDepartmentBtn').attr('onclick', 'updateDepartment(' + deptId + ')');

            // إظهار النافذة
            $('#addDepartmentModal').modal('show');
        })
        .fail(function() {
            alert('حدث خطأ في جلب بيانات القسم');
        });
}

function updateDepartment(deptId) {
    var formData = {
        name: $('#deptName').val(),
        code: $('#deptCode').val(),
        description: $('#deptDescription').val(),
        is_active: $('#deptActive').is(':checked')
    };

    $.ajax({
        url: '/api/departments/' + deptId,
        type: 'PUT',
        data: formData,
        success: function(response) {
            $('#addDepartmentModal').modal('hide');
            location.reload();
        },
        error: function(xhr) {
            alert('حدث خطأ: ' + xhr.responseJSON.message);
        }
    });
}

function deleteDepartment(deptId) {
    if (confirm('هل أنت متأكد من حذف هذا القسم؟\nسيتم حذف جميع البيانات المرتبطة به.')) {
        $.ajax({
            url: '/api/departments/' + deptId,
            type: 'DELETE',
            success: function(response) {
                location.reload();
            },
            error: function(xhr) {
                alert('حدث خطأ: ' + xhr.responseJSON.message);
            }
        });
    }
}

function editDocumentType(typeId) {
    // جلب بيانات نوع الكتاب
    $.get('/api/document-types/' + typeId)
        .done(function(type) {
            // ملء النموذج ببيانات النوع
            $('#typeName').val(type.name);
            $('#typeCode').val(type.code);
            $('#typeDescription').val(type.description);
            $('#typeActive').prop('checked', type.is_active);

            // تغيير عنوان النافذة
            $('#addDocumentTypeModal .modal-title').text('تعديل نوع الكتاب');

            // تغيير زر الحفظ
            $('#saveDocumentTypeBtn').attr('onclick', 'updateDocumentType(' + typeId + ')');

            // إظهار النافذة
            $('#addDocumentTypeModal').modal('show');
        })
        .fail(function() {
            alert('حدث خطأ في جلب بيانات نوع الكتاب');
        });
}

function updateDocumentType(typeId) {
    var formData = {
        name: $('#typeName').val(),
        code: $('#typeCode').val(),
        description: $('#typeDescription').val(),
        is_active: $('#typeActive').is(':checked')
    };

    $.ajax({
        url: '/api/document-types/' + typeId,
        type: 'PUT',
        data: formData,
        success: function(response) {
            $('#addDocumentTypeModal').modal('hide');
            location.reload();
        },
        error: function(xhr) {
            alert('حدث خطأ: ' + xhr.responseJSON.message);
        }
    });
}

function deleteDocumentType(typeId) {
    if (confirm('هل أنت متأكد من حذف هذا النوع؟\nسيتم حذف جميع البيانات المرتبطة به.')) {
        $.ajax({
            url: '/api/document-types/' + typeId,
            type: 'DELETE',
            success: function(response) {
                location.reload();
            },
            error: function(xhr) {
                alert('حدث خطأ: ' + xhr.responseJSON.message);
            }
        });
    }
}

$('#generalSettingsForm').on('submit', function(e) {
    e.preventDefault();
    alert('تم حفظ الإعدادات بنجاح');
});
</script>
{% endblock %}
