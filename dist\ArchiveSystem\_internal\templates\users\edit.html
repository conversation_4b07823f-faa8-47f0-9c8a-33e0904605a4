{% extends "base.html" %}

{% block title %}تعديل المستخدم - نظام الأرشفة الإلكترونية{% endblock %}

{% block content %}
<div class="page-header">
    <h1><i class="fas fa-user-edit me-2"></i>تعديل المستخدم: {{ user.full_name }}</h1>
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ url_for('dashboard') }}">لوحة التحكم</a></li>
            <li class="breadcrumb-item"><a href="{{ url_for('users') }}">إدارة المستخدمين</a></li>
            <li class="breadcrumb-item active">تعديل المستخدم</li>
        </ol>
    </nav>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-user-cog me-2"></i>
                    معلومات المستخدم
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    {{ form.hidden_tag() }}
                    
                    <div class="mb-3">
                        {{ form.username.label(class="form-label") }}
                        {{ form.username(class="form-control" + (" is-invalid" if form.username.errors else "")) }}
                        {% if form.username.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.username.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                        <div class="form-text">اسم المستخدم للدخول إلى النظام</div>
                    </div>
                    
                    <div class="mb-3">
                        {{ form.full_name.label(class="form-label") }}
                        {{ form.full_name(class="form-control" + (" is-invalid" if form.full_name.errors else "")) }}
                        {% if form.full_name.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.full_name.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.password.label(class="form-label") }}
                        {{ form.password(class="form-control" + (" is-invalid" if form.password.errors else "")) }}
                        {% if form.password.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.password.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                        <div class="form-text">اتركه فارغاً إذا كنت لا تريد تغيير كلمة المرور</div>
                    </div>
                    
                    <div class="mb-3">
                        {{ form.role.label(class="form-label") }}
                        {{ form.role(class="form-select" + (" is-invalid" if form.role.errors else "")) }}
                        {% if form.role.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.role.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.department.label(class="form-label") }}
                        {{ form.department(class="form-control" + (" is-invalid" if form.department.errors else "")) }}
                        {% if form.department.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.department.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="form-check mb-3">
                        {{ form.is_active(class="form-check-input") }}
                        {{ form.is_active.label(class="form-check-label") }}
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('users') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-right me-2"></i>
                            إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            حفظ التغييرات
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات إضافية
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <strong>تاريخ الإنشاء:</strong><br>
                    <small class="text-muted">{{ user.created_at.strftime('%Y-%m-%d %H:%M') if user.created_at else 'غير محدد' }}</small>
                </div>
                
                <div class="mb-3">
                    <strong>آخر دخول:</strong><br>
                    <small class="text-muted">
                        {% if user.last_login %}
                            {{ user.last_login.strftime('%Y-%m-%d %H:%M') }}
                        {% else %}
                            لم يسجل دخول بعد
                        {% endif %}
                    </small>
                </div>
                
                <div class="mb-3">
                    <strong>الحالة:</strong><br>
                    {% if user.is_active %}
                        <span class="badge bg-success">نشط</span>
                    {% else %}
                        <span class="badge bg-secondary">غير نشط</span>
                    {% endif %}
                </div>
                
                <div class="mb-3">
                    <strong>البريد الإلكتروني:</strong><br>
                    <small class="text-muted">{{ user.email }}</small>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-shield-alt me-2"></i>
                    الصلاحيات
                </h5>
            </div>
            <div class="card-body">
                <div id="role-permissions">
                    <!-- سيتم ملؤها بـ JavaScript -->
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // تحديث معلومات الدور عند التغيير
    $('#role').change(updateRolePermissions);
    
    // تحديث الصلاحيات عند تحميل الصفحة
    updateRolePermissions();
    
    function updateRolePermissions() {
        var role = $('#role').val();
        var permissions = '';
        
        switch(role) {
            case 'admin':
                permissions = `
                    <div class="alert alert-danger">
                        <strong>مدير النظام</strong><br>
                        <small>
                            • إدارة كاملة للنظام<br>
                            • إدارة المستخدمين والصلاحيات<br>
                            • إدارة الإعدادات والنسخ الاحتياطي<br>
                            • جميع صلاحيات الكتب والتقارير
                        </small>
                    </div>
                `;
                break;
            case 'manager':
                permissions = `
                    <div class="alert alert-warning">
                        <strong>مدير</strong><br>
                        <small>
                            • إدارة الكتب والوثائق<br>
                            • عرض التقارير والإحصائيات<br>
                            • تحويل الكتب بين المستخدمين<br>
                            • البحث المتقدم
                        </small>
                    </div>
                `;
                break;
            case 'user':
                permissions = `
                    <div class="alert alert-primary">
                        <strong>مستخدم</strong><br>
                        <small>
                            • إضافة وتعديل الكتب<br>
                            • البحث في الكتب<br>
                            • تحويل الكتب<br>
                            • عرض الكتب المخصصة له
                        </small>
                    </div>
                `;
                break;
            case 'viewer':
                permissions = `
                    <div class="alert alert-secondary">
                        <strong>مشاهد</strong><br>
                        <small>
                            • عرض الكتب فقط<br>
                            • البحث في الكتب<br>
                            • لا يمكنه التعديل أو الإضافة
                        </small>
                    </div>
                `;
                break;
        }
        
        $('#role-permissions').html(permissions);
    }
});
</script>
{% endblock %}
