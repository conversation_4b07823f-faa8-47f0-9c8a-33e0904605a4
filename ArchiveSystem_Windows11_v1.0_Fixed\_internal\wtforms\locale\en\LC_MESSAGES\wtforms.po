# English translations for WTForms.
# Copyright (C) 2020 WTForms Team
# This file is distributed under the same license as the WTForms project.
# <AUTHOR> <EMAIL>, 2020.
#
msgid ""
msgstr ""
"Project-Id-Version: WTForms 1.0.4\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2020-04-25 11:34-0700\n"
"PO-Revision-Date: 2013-04-28 15:36-0700\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language: en\n"
"Language-Team: en_US <<EMAIL>>\n"
"Plural-Forms: nplurals=2; plural=(n != 1)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.8.0\n"

#: src/wtforms/validators.py:87
#, python-format
msgid "Invalid field name '%s'."
msgstr "Invalid field name '%s'."

#: src/wtforms/validators.py:98
#, python-format
msgid "Field must be equal to %(other_name)s."
msgstr "Field must be equal to %(other_name)s."

#: src/wtforms/validators.py:134
#, python-format
msgid "Field must be at least %(min)d character long."
msgid_plural "Field must be at least %(min)d characters long."
msgstr[0] "Field must be at least %(min)d character long."
msgstr[1] "Field must be at least %(min)d characters long."

#: src/wtforms/validators.py:140
#, python-format
msgid "Field cannot be longer than %(max)d character."
msgid_plural "Field cannot be longer than %(max)d characters."
msgstr[0] "Field cannot be longer than %(max)d character."
msgstr[1] "Field cannot be longer than %(max)d characters."

#: src/wtforms/validators.py:146
#, python-format
msgid "Field must be exactly %(max)d character long."
msgid_plural "Field must be exactly %(max)d characters long."
msgstr[0] "Field must be exactly %(max)d character long."
msgstr[1] "Field must be exactly %(max)d characters long."

#: src/wtforms/validators.py:152
#, python-format
msgid "Field must be between %(min)d and %(max)d characters long."
msgstr "Field must be between %(min)d and %(max)d characters long."

#: src/wtforms/validators.py:197
#, python-format
msgid "Number must be at least %(min)s."
msgstr "Number must be at least %(min)s."

#: src/wtforms/validators.py:199
#, python-format
msgid "Number must be at most %(max)s."
msgstr "Number must be at most %(max)s."

#: src/wtforms/validators.py:201
#, python-format
msgid "Number must be between %(min)s and %(max)s."
msgstr "Number must be between %(min)s and %(max)s."

#: src/wtforms/validators.py:269 src/wtforms/validators.py:294
msgid "This field is required."
msgstr "This field is required."

#: src/wtforms/validators.py:327
msgid "Invalid input."
msgstr "Invalid input."

#: src/wtforms/validators.py:387
msgid "Invalid email address."
msgstr "Invalid email address."

#: src/wtforms/validators.py:423
msgid "Invalid IP address."
msgstr "Invalid IP address."

#: src/wtforms/validators.py:466
msgid "Invalid Mac address."
msgstr "Invalid MAC address."

#: src/wtforms/validators.py:501
msgid "Invalid URL."
msgstr "Invalid URL."

#: src/wtforms/validators.py:522
msgid "Invalid UUID."
msgstr "Invalid UUID."

#: src/wtforms/validators.py:553
#, python-format
msgid "Invalid value, must be one of: %(values)s."
msgstr "Invalid value, must be one of: %(values)s."

#: src/wtforms/validators.py:588
#, python-format
msgid "Invalid value, can't be any of: %(values)s."
msgstr "Invalid value, can't be any of: %(values)s."

#: src/wtforms/csrf/core.py:96
msgid "Invalid CSRF Token."
msgstr "Invalid CSRF Token."

#: src/wtforms/csrf/session.py:63
msgid "CSRF token missing."
msgstr "CSRF token missing."

#: src/wtforms/csrf/session.py:71
msgid "CSRF failed."
msgstr "CSRF failed."

#: src/wtforms/csrf/session.py:76
msgid "CSRF token expired."
msgstr "CSRF token expired."

#: src/wtforms/fields/core.py:534
msgid "Invalid Choice: could not coerce."
msgstr "Invalid Choice: could not coerce."

#: src/wtforms/fields/core.py:538
msgid "Choices cannot be None."
msgstr "Choices cannot be None."

#: src/wtforms/fields/core.py:545
msgid "Not a valid choice."
msgstr "Not a valid choice."

#: src/wtforms/fields/core.py:573
msgid "Invalid choice(s): one or more data inputs could not be coerced."
msgstr "Invalid choice(s): one or more data inputs could not be coerced."

#: src/wtforms/fields/core.py:584
#, python-format
msgid "'%(value)s' is not a valid choice for this field."
msgstr "'%(value)s' is not a valid choice for this field."

#: src/wtforms/fields/core.py:679 src/wtforms/fields/core.py:689
msgid "Not a valid integer value."
msgstr "Not a valid integer value."

#: src/wtforms/fields/core.py:760
msgid "Not a valid decimal value."
msgstr "Not a valid decimal value."

#: src/wtforms/fields/core.py:788
msgid "Not a valid float value."
msgstr "Not a valid float value."

#: src/wtforms/fields/core.py:853
msgid "Not a valid datetime value."
msgstr "Not a valid datetime value."

#: src/wtforms/fields/core.py:871
msgid "Not a valid date value."
msgstr "Not a valid date value."

#: src/wtforms/fields/core.py:889
msgid "Not a valid time value."
msgstr "Not a valid time value."
