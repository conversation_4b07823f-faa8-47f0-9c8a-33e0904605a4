#!/usr/bin/env python3
"""
اختبار بسيط للخادم
"""

from flask import Flask, jsonify, send_file
import openpyxl
import io
from datetime import datetime

app = Flask(__name__)

@app.route('/')
def index():
    return jsonify({"message": "الخادم يعمل بشكل صحيح", "time": datetime.now().isoformat()})

@app.route('/test-excel')
def test_excel():
    """اختبار تصدير Excel بسيط"""
    try:
        # إنشاء ملف Excel بسيط
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "اختبار"
        
        ws['A1'] = "اختبار التصدير"
        ws['A2'] = f"التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M')}"
        ws['A3'] = "رقم"
        ws['B3'] = "الوصف"
        
        # إضافة بيانات تجريبية
        for i in range(1, 6):
            ws[f'A{i+3}'] = f"TEST-{i:03d}"
            ws[f'B{i+3}'] = f"وصف تجريبي {i}"
        
        # حفظ في الذاكرة
        output = io.BytesIO()
        wb.save(output)
        output.seek(0)
        
        return send_file(
            output,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name='test_simple.xlsx'
        )
        
    except Exception as e:
        return jsonify({"error": str(e)}), 500

if __name__ == '__main__':
    print("بدء تشغيل خادم الاختبار على http://127.0.0.1:5001")
    app.run(debug=True, host='127.0.0.1', port=5001)
