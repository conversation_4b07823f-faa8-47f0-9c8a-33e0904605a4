{% extends "base.html" %}

{% block title %}نتائج التقرير - نظام الأرشفة الإلكترونية{% endblock %}

{% block content %}
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1><i class="fas fa-chart-line me-2"></i>نتائج التقرير</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('dashboard') }}">لوحة التحكم</a></li>
                    <li class="breadcrumb-item"><a href="{{ url_for('reports') }}">التقارير</a></li>
                    <li class="breadcrumb-item active">النتائج</li>
                </ol>
            </nav>
        </div>
        <div class="btn-group" role="group">
            <button type="button" class="btn btn-outline-success" onclick="exportReport('excel')">
                <i class="fas fa-file-excel me-1"></i>
                Excel
            </button>
            <button type="button" class="btn btn-outline-danger" onclick="exportReport('pdf')">
                <i class="fas fa-file-pdf me-1"></i>
                PDF
            </button>
            <button type="button" class="btn btn-outline-secondary" onclick="window.print()">
                <i class="fas fa-print me-1"></i>
                طباعة
            </button>
        </div>
    </div>
</div>

<!-- معلومات التقرير -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-info-circle me-2"></i>
            معلومات التقرير
        </h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-3">
                <strong>نوع التقرير:</strong>
                {% if report_type == 'documents_summary' %}
                    ملخص الكتب
                {% elif report_type == 'documents_by_type' %}
                    الكتب حسب النوع
                {% elif report_type == 'documents_by_department' %}
                    الكتب حسب القسم
                {% elif report_type == 'user_activity' %}
                    نشاط المستخدمين
                {% endif %}
            </div>
            <div class="col-md-3">
                <strong>من تاريخ:</strong> {{ date_from.strftime('%Y-%m-%d') }}
            </div>
            <div class="col-md-3">
                <strong>إلى تاريخ:</strong> {{ date_to.strftime('%Y-%m-%d') }}
            </div>
            <div class="col-md-3">
                <strong>تاريخ الإنشاء:</strong> {{ moment().strftime('%Y-%m-%d %H:%M') }}
            </div>
        </div>
    </div>
</div>

{% if report_type == 'documents_summary' %}
<!-- ملخص الكتب -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="stats-card">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <div class="stats-number">{{ report_data.total }}</div>
                    <div class="stats-label">إجمالي الكتب</div>
                </div>
                <div class="stats-icon">
                    <i class="fas fa-file-alt fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <div class="stats-number">{{ report_data.incoming }}</div>
                    <div class="stats-label">كتب واردة</div>
                </div>
                <div class="stats-icon">
                    <i class="fas fa-inbox fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <div class="stats-number">{{ report_data.outgoing }}</div>
                    <div class="stats-label">كتب صادرة</div>
                </div>
                <div class="stats-icon">
                    <i class="fas fa-paper-plane fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <div class="stats-number">{{ report_data.internal }}</div>
                    <div class="stats-label">كتب داخلية</div>
                </div>
                <div class="stats-icon">
                    <i class="fas fa-building fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">توزيع الكتب حسب النوع</h5>
            </div>
            <div class="card-body">
                <canvas id="typeChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">توزيع الكتب حسب الأولوية</h5>
            </div>
            <div class="card-body">
                <canvas id="priorityChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
</div>

{% elif report_type == 'documents_by_type' or report_type == 'documents_by_department' or report_type == 'user_activity' %}
<!-- تقارير أخرى -->
<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    {% if report_type == 'documents_by_type' %}
                        توزيع الكتب حسب النوع
                    {% elif report_type == 'documents_by_department' %}
                        توزيع الكتب حسب القسم
                    {% elif report_type == 'user_activity' %}
                        نشاط المستخدمين
                    {% endif %}
                </h5>
            </div>
            <div class="card-body">
                <canvas id="mainChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">التفاصيل</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>البند</th>
                                <th>العدد</th>
                                <th>النسبة</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% set total = report_data.values() | sum %}
                            {% for key, value in report_data.items() %}
                            <tr>
                                <td>{{ key }}</td>
                                <td>{{ value }}</td>
                                <td>{{ ((value / total * 100) | round(1)) }}%</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- قائمة الكتب التفصيلية -->
{% if documents %}
<div class="card mt-4">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-list me-2"></i>
            قائمة الكتب التفصيلية ({{ documents|length }} كتاب)
        </h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-light">
                    <tr>
                        <th>رقم الكتاب</th>
                        <th>الموضوع</th>
                        <th>النوع</th>
                        <th>التاريخ</th>
                        <th>الأولوية</th>
                        <th>المنشئ</th>
                    </tr>
                </thead>
                <tbody>
                    {% for doc in documents %}
                    <tr>
                        <td>
                            <a href="{{ url_for('view_document', id=doc.id) }}" class="text-decoration-none">
                                {{ doc.get_full_number() }}
                            </a>
                        </td>
                        <td>
                            <div class="text-truncate" style="max-width: 200px;" title="{{ doc.subject }}">
                                {{ doc.subject }}
                            </div>
                        </td>
                        <td>
                            {% if doc.document_type == 'incoming' %}
                                <span class="badge bg-success">وارد</span>
                            {% elif doc.document_type == 'outgoing' %}
                                <span class="badge bg-info">صادر</span>
                            {% else %}
                                <span class="badge bg-warning">داخلي</span>
                            {% endif %}
                        </td>
                        <td>{{ doc.document_date.strftime('%Y-%m-%d') }}</td>
                        <td>
                            {% if doc.priority == 'urgent' %}
                                <span class="badge bg-danger">عاجل</span>
                            {% elif doc.priority == 'high' %}
                                <span class="badge bg-warning">عالي</span>
                            {% elif doc.priority == 'low' %}
                                <span class="badge bg-secondary">منخفض</span>
                            {% else %}
                                <span class="badge bg-light text-dark">عادي</span>
                            {% endif %}
                        </td>
                        <td>{{ doc.creator.full_name }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_css %}
<style>
@media print {
    .btn-group, .page-header .btn-group {
        display: none !important;
    }
    
    .card {
        border: 1px solid #000 !important;
        break-inside: avoid;
    }
    
    .stats-card {
        border: 1px solid #000 !important;
        color: #000 !important;
        background: #fff !important;
    }
}
</style>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
{% if report_type == 'documents_summary' %}
// رسم بياني لأنواع الكتب
var typeCtx = document.getElementById('typeChart').getContext('2d');
var typeChart = new Chart(typeCtx, {
    type: 'doughnut',
    data: {
        labels: ['وارد', 'صادر', 'داخلي'],
        datasets: [{
            data: [{{ report_data.incoming }}, {{ report_data.outgoing }}, {{ report_data.internal }}],
            backgroundColor: ['#28a745', '#17a2b8', '#ffc107']
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});

// رسم بياني للأولوية
var priorityCtx = document.getElementById('priorityChart').getContext('2d');
var priorityChart = new Chart(priorityCtx, {
    type: 'bar',
    data: {
        labels: ['عاجل', 'عالي', 'عادي', 'منخفض'],
        datasets: [{
            label: 'عدد الكتب',
            data: [{{ report_data.urgent }}, {{ report_data.high }}, {{ report_data.normal }}, {{ report_data.low }}],
            backgroundColor: ['#dc3545', '#ffc107', '#6c757d', '#28a745']
        }]
    },
    options: {
        responsive: true,
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

{% elif report_type in ['documents_by_type', 'documents_by_department', 'user_activity'] %}
// رسم بياني للتقارير الأخرى
var mainCtx = document.getElementById('mainChart').getContext('2d');
var mainChart = new Chart(mainCtx, {
    type: 'pie',
    data: {
        labels: {{ report_data.keys() | list | tojson }},
        datasets: [{
            data: {{ report_data.values() | list | tojson }},
            backgroundColor: [
                '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', 
                '#9966FF', '#FF9F40', '#FF6384', '#C9CBCF'
            ]
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});
{% endif %}

function exportReport(format) {
    var params = new URLSearchParams({
        'report_type': '{{ report_type }}',
        'date_from': '{{ date_from }}',
        'date_to': '{{ date_to }}',
        'format': format
    });
    
    window.open('/export-report?' + params.toString(), '_blank');
}
</script>
{% endblock %}
