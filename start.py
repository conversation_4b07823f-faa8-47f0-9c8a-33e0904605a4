#!/usr/bin/env python3
"""
ملف تشغيل نظام الأرشفة الإلكترونية مع إظهار المخرجات
"""

import sys
import os

def main():
    print("🗂️  نظام الأرشفة الإلكترونية للكتب الرسمية")
    print("=" * 60)
    
    # التحقق من Flask
    try:
        import flask
        print("✅ Flask متوفر - الإصدار:", flask.__version__)
    except ImportError:
        print("❌ Flask غير مثبت!")
        print("يرجى تثبيته باستخدام: pip install flask")
        return
    
    # التحقق من باقي المتطلبات
    try:
        import flask_sqlalchemy
        import flask_login
        import flask_wtf
        print("✅ جميع المتطلبات متوفرة")
    except ImportError as e:
        print(f"❌ مكتبة مفقودة: {e}")
        return
    
    # إعداد قاعدة البيانات
    print("🔧 إعداد قاعدة البيانات...")
    try:
        from app import app
        from models import db, User, Department, DocumentType
        
        with app.app_context():
            db.create_all()
            print("✅ تم إنشاء قاعدة البيانات")
            
            # إنشاء المستخدم الافتراضي
            if not User.query.filter_by(username='admin').first():
                admin_user = User(
                    username='admin',
                    email='<EMAIL>',
                    full_name='مدير النظام',
                    role='admin',
                    department='إدارة النظام',
                    is_active=True
                )
                admin_user.set_password('admin123')
                db.session.add(admin_user)
                db.session.commit()
                print("✅ تم إنشاء المستخدم الافتراضي: admin / admin123")
            else:
                print("✅ المستخدم الافتراضي موجود بالفعل")
    
    except Exception as e:
        print(f"❌ خطأ في إعداد قاعدة البيانات: {e}")
        return
    
    # تشغيل الخادم
    print("\n" + "=" * 60)
    print("🚀 بدء تشغيل الخادم...")
    print("📍 الرابط: http://localhost:5000")
    print("👤 تسجيل الدخول: admin / admin123")
    print("⏹️  للإيقاف: اضغط Ctrl+C")
    print("=" * 60)
    
    try:
        app.run(debug=True, host='0.0.0.0', port=5000, use_reloader=False)
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف الخادم")
    except Exception as e:
        print(f"❌ خطأ في تشغيل الخادم: {e}")

if __name__ == '__main__':
    main()
