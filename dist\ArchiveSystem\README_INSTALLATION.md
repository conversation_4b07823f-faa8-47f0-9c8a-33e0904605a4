# 🏛️ نظام الأرشفة الإلكترونية - دليل التثبيت والتشغيل

## 📋 نظرة عامة
نظام الأرشفة الإلكترونية هو تطبيق ويب متكامل لإدارة الوثائق والكتب الإلكترونية مع إمكانيات البحث والتصنيف والتقارير.

## 🎯 متطلبات النظام
- **نظام التشغيل**: Windows 10/11 (64-bit)
- **الذاكرة**: 4 GB RAM كحد أدنى (8 GB مُوصى به)
- **مساحة القرص**: 500 MB مساحة فارغة
- **المتصفح**: Chrome, Firefox, Edge, أو Safari

## 📦 محتويات الحزمة
```
ArchiveSystem/
├── ArchiveSystem.exe          # الملف التنفيذي الرئيسي
├── start_archive_system.bat   # ملف بدء التشغيل السريع
├── _internal/                 # ملفات النظام (لا تحذف)
├── templates/                 # قوالب الواجهة
├── static/                    # الملفات الثابتة (CSS, JS, صور)
├── instance/                  # قاعدة البيانات (تُنشأ تلقائياً)
├── uploads/                   # مجلد الملفات المرفوعة
├── backups/                   # مجلد النسخ الاحتياطية
└── README_INSTALLATION.md     # هذا الملف
```

## 🚀 طريقة التشغيل

### الطريقة الأولى: التشغيل السريع
1. **انقر نقراً مزدوجاً** على ملف `start_archive_system.bat`
2. **انتظر** حتى يبدأ النظام (قد يستغرق 10-30 ثانية في المرة الأولى)
3. **سيفتح المتصفح تلقائياً** على عنوان النظام

### الطريقة الثانية: التشغيل المباشر
1. **انقر نقراً مزدوجاً** على ملف `ArchiveSystem.exe`
2. **انتظر** ظهور رسالة "الخادم يعمل على"
3. **افتح المتصفح** واذهب إلى العنوان المعروض

## 🔐 تسجيل الدخول الأول
عند التشغيل الأول، استخدم البيانات التالية:
- **اسم المستخدم**: `admin`
- **كلمة المرور**: `admin123`

> ⚠️ **مهم**: يُنصح بتغيير كلمة المرور فوراً بعد تسجيل الدخول الأول

## 🛠️ إعدادات إضافية

### تغيير المنفذ
إذا كان المنفذ 5000 مُستخدماً، سيبحث النظام تلقائياً عن منفذ متاح آخر.

### إعداد قاعدة البيانات
- قاعدة البيانات تُنشأ تلقائياً في المجلد `instance/`
- لا حاجة لتثبيت أي برامج إضافية

### النسخ الاحتياطية
- يمكن إنشاء نسخ احتياطية من داخل النظام
- النسخ تُحفظ في مجلد `backups/`

## 🔧 حل المشاكل الشائعة

### المشكلة: لا يبدأ النظام
**الحل**:
1. تأكد من أن Windows Defender لا يحجب الملف
2. شغّل الملف كمدير (Run as Administrator)
3. تأكد من وجود جميع الملفات في نفس المجلد

### المشكلة: لا يفتح المتصفح تلقائياً
**الحل**:
1. افتح المتصفح يدوياً
2. اذهب إلى `http://localhost:5000`
3. إذا لم يعمل، جرب المنفذ المعروض في نافذة التشغيل

### المشكلة: رسالة خطأ "Port already in use"
**الحل**:
- النظام سيبحث تلقائياً عن منفذ متاح آخر
- استخدم العنوان الجديد المعروض

### المشكلة: بطء في التحميل
**الحل**:
1. تأكد من وجود مساحة كافية على القرص
2. أغلق البرامج غير الضرورية
3. في المرة الأولى، قد يستغرق وقتاً أطول

## 📞 الدعم الفني

### معلومات النظام
- **الإصدار**: 1.0.0
- **تاريخ البناء**: 2024-12-25
- **نوع البناء**: Standalone Windows Package

### تسجيل الأخطاء
إذا واجهت مشاكل:
1. احتفظ بنافذة التشغيل مفتوحة لرؤية رسائل الخطأ
2. التقط صورة شاشة للخطأ
3. تحقق من ملفات السجل في مجلد النظام

## 🔄 التحديثات
- للحصول على آخر التحديثات، قم بتحميل الإصدار الجديد
- احتفظ بنسخة احتياطية من مجلد `instance/` قبل التحديث

## 📝 ملاحظات مهمة
1. **لا تحذف** مجلد `_internal/` - يحتوي على ملفات النظام الأساسية
2. **احتفظ بنسخ احتياطية** دورية من قاعدة البيانات
3. **استخدم متصفح حديث** للحصول على أفضل تجربة
4. **أغلق النظام بشكل صحيح** باستخدام Ctrl+C في نافذة التشغيل

## 🎉 مرحباً بك في نظام الأرشفة الإلكترونية!
نتمنى لك تجربة ممتعة ومفيدة مع النظام. 🚀
