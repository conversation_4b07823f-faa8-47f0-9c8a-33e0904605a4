('C:\\Users\\<USER>\\OneDrive\\Desktop\\test '
 '111\\safaa\\build\\archive_system\\PYZ-00.pyz',
 [('PIL',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\__init__.py',
   'PYMODULE'),
  ('PIL.AvifImagePlugin',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\AvifImagePlugin.py',
   'PYMODULE'),
  ('PIL.BdfFontFile',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\BdfFontFile.py',
   'PYMODULE'),
  ('PIL.BlpImagePlugin',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\BlpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BmpImagePlugin',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\BmpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BufrStubImagePlugin',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.ContainerIO',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\ContainerIO.py',
   'PYMODULE'),
  ('PIL.CurImagePlugin',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\CurImagePlugin.py',
   'PYMODULE'),
  ('PIL.DcxImagePlugin',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\DcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.DdsImagePlugin',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\DdsImagePlugin.py',
   'PYMODULE'),
  ('PIL.EpsImagePlugin',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\EpsImagePlugin.py',
   'PYMODULE'),
  ('PIL.ExifTags',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\ExifTags.py',
   'PYMODULE'),
  ('PIL.FitsImagePlugin',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\FitsImagePlugin.py',
   'PYMODULE'),
  ('PIL.FliImagePlugin',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\FliImagePlugin.py',
   'PYMODULE'),
  ('PIL.FontFile',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\FontFile.py',
   'PYMODULE'),
  ('PIL.FpxImagePlugin',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\FpxImagePlugin.py',
   'PYMODULE'),
  ('PIL.FtexImagePlugin',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\FtexImagePlugin.py',
   'PYMODULE'),
  ('PIL.GbrImagePlugin',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\GbrImagePlugin.py',
   'PYMODULE'),
  ('PIL.GdImageFile',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\GdImageFile.py',
   'PYMODULE'),
  ('PIL.GifImagePlugin',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\GifImagePlugin.py',
   'PYMODULE'),
  ('PIL.GimpGradientFile',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\GimpGradientFile.py',
   'PYMODULE'),
  ('PIL.GimpPaletteFile',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\GimpPaletteFile.py',
   'PYMODULE'),
  ('PIL.GribStubImagePlugin',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\GribStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.Hdf5StubImagePlugin',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcnsImagePlugin',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\IcnsImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcoImagePlugin',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\IcoImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImImagePlugin',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\ImImagePlugin.py',
   'PYMODULE'),
  ('PIL.Image',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\Image.py',
   'PYMODULE'),
  ('PIL.ImageChops',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\ImageChops.py',
   'PYMODULE'),
  ('PIL.ImageCms',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\ImageCms.py',
   'PYMODULE'),
  ('PIL.ImageColor',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\ImageColor.py',
   'PYMODULE'),
  ('PIL.ImageDraw',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\ImageDraw.py',
   'PYMODULE'),
  ('PIL.ImageDraw2',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\ImageDraw2.py',
   'PYMODULE'),
  ('PIL.ImageEnhance',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\ImageEnhance.py',
   'PYMODULE'),
  ('PIL.ImageFile',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\ImageFile.py',
   'PYMODULE'),
  ('PIL.ImageFilter',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\ImageFilter.py',
   'PYMODULE'),
  ('PIL.ImageFont',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\ImageFont.py',
   'PYMODULE'),
  ('PIL.ImageGrab',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\ImageGrab.py',
   'PYMODULE'),
  ('PIL.ImageMath',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\ImageMath.py',
   'PYMODULE'),
  ('PIL.ImageMode',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\ImageMode.py',
   'PYMODULE'),
  ('PIL.ImageMorph',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\ImageMorph.py',
   'PYMODULE'),
  ('PIL.ImageOps',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\ImageOps.py',
   'PYMODULE'),
  ('PIL.ImagePalette',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\ImagePalette.py',
   'PYMODULE'),
  ('PIL.ImagePath',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\ImagePath.py',
   'PYMODULE'),
  ('PIL.ImageQt',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\ImageQt.py',
   'PYMODULE'),
  ('PIL.ImageSequence',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\ImageSequence.py',
   'PYMODULE'),
  ('PIL.ImageShow',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\ImageShow.py',
   'PYMODULE'),
  ('PIL.ImageStat',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\ImageStat.py',
   'PYMODULE'),
  ('PIL.ImageTk',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\ImageTk.py',
   'PYMODULE'),
  ('PIL.ImageTransform',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\ImageTransform.py',
   'PYMODULE'),
  ('PIL.ImageWin',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\ImageWin.py',
   'PYMODULE'),
  ('PIL.ImtImagePlugin',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\ImtImagePlugin.py',
   'PYMODULE'),
  ('PIL.IptcImagePlugin',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\IptcImagePlugin.py',
   'PYMODULE'),
  ('PIL.Jpeg2KImagePlugin',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegImagePlugin',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\JpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegPresets',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\JpegPresets.py',
   'PYMODULE'),
  ('PIL.McIdasImagePlugin',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\McIdasImagePlugin.py',
   'PYMODULE'),
  ('PIL.MicImagePlugin',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\MicImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpegImagePlugin',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\MpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpoImagePlugin',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\MpoImagePlugin.py',
   'PYMODULE'),
  ('PIL.MspImagePlugin',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\MspImagePlugin.py',
   'PYMODULE'),
  ('PIL.PSDraw',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\PSDraw.py',
   'PYMODULE'),
  ('PIL.PaletteFile',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\PaletteFile.py',
   'PYMODULE'),
  ('PIL.PalmImagePlugin',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\PalmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcdImagePlugin',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\PcdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcfFontFile',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\PcfFontFile.py',
   'PYMODULE'),
  ('PIL.PcxImagePlugin',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\PcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfImagePlugin',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\PdfImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfParser',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\PdfParser.py',
   'PYMODULE'),
  ('PIL.PixarImagePlugin',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\PixarImagePlugin.py',
   'PYMODULE'),
  ('PIL.PngImagePlugin',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\PngImagePlugin.py',
   'PYMODULE'),
  ('PIL.PpmImagePlugin',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\PpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PsdImagePlugin',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\PsdImagePlugin.py',
   'PYMODULE'),
  ('PIL.QoiImagePlugin',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\QoiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SgiImagePlugin',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\SgiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SpiderImagePlugin',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\SpiderImagePlugin.py',
   'PYMODULE'),
  ('PIL.SunImagePlugin',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\SunImagePlugin.py',
   'PYMODULE'),
  ('PIL.TarIO',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\TarIO.py',
   'PYMODULE'),
  ('PIL.TgaImagePlugin',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\TgaImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffImagePlugin',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\TiffImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffTags',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\TiffTags.py',
   'PYMODULE'),
  ('PIL.WalImageFile',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\WalImageFile.py',
   'PYMODULE'),
  ('PIL.WebPImagePlugin',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\WebPImagePlugin.py',
   'PYMODULE'),
  ('PIL.WmfImagePlugin',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\WmfImagePlugin.py',
   'PYMODULE'),
  ('PIL.XVThumbImagePlugin',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'PYMODULE'),
  ('PIL.XbmImagePlugin',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\XbmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XpmImagePlugin',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\XpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.__main__',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\__main__.py',
   'PYMODULE'),
  ('PIL._binary',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\_binary.py',
   'PYMODULE'),
  ('PIL._deprecate',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\_deprecate.py',
   'PYMODULE'),
  ('PIL._tkinter_finder',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\_tkinter_finder.py',
   'PYMODULE'),
  ('PIL._typing',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\_typing.py',
   'PYMODULE'),
  ('PIL._util',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\_util.py',
   'PYMODULE'),
  ('PIL._version',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\_version.py',
   'PYMODULE'),
  ('PIL.features',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\features.py',
   'PYMODULE'),
  ('PIL.report',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\PIL\\report.py',
   'PYMODULE'),
  ('__future__',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\__future__.py',
   'PYMODULE'),
  ('_aix_support',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_aix_support.py',
   'PYMODULE'),
  ('_colorize',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_colorize.py',
   'PYMODULE'),
  ('_compat_pickle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_compression.py',
   'PYMODULE'),
  ('_distutils_hack',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('_ios_support',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_ios_support.py',
   'PYMODULE'),
  ('_markupbase',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_markupbase.py',
   'PYMODULE'),
  ('_opcode_metadata',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_opcode_metadata.py',
   'PYMODULE'),
  ('_py_abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_py_abc.py',
   'PYMODULE'),
  ('_pydatetime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pydatetime.py',
   'PYMODULE'),
  ('_pydecimal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pydecimal.py',
   'PYMODULE'),
  ('_pyrepl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\__init__.py',
   'PYMODULE'),
  ('_pyrepl._minimal_curses',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\_minimal_curses.py',
   'PYMODULE'),
  ('_pyrepl._threading_handler',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\_threading_handler.py',
   'PYMODULE'),
  ('_pyrepl.base_eventqueue',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\base_eventqueue.py',
   'PYMODULE'),
  ('_pyrepl.commands',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\commands.py',
   'PYMODULE'),
  ('_pyrepl.completing_reader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\completing_reader.py',
   'PYMODULE'),
  ('_pyrepl.console',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\console.py',
   'PYMODULE'),
  ('_pyrepl.curses',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\curses.py',
   'PYMODULE'),
  ('_pyrepl.fancy_termios',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\fancy_termios.py',
   'PYMODULE'),
  ('_pyrepl.historical_reader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\historical_reader.py',
   'PYMODULE'),
  ('_pyrepl.input',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\input.py',
   'PYMODULE'),
  ('_pyrepl.keymap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\keymap.py',
   'PYMODULE'),
  ('_pyrepl.main',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\main.py',
   'PYMODULE'),
  ('_pyrepl.pager',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\pager.py',
   'PYMODULE'),
  ('_pyrepl.reader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\reader.py',
   'PYMODULE'),
  ('_pyrepl.readline',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\readline.py',
   'PYMODULE'),
  ('_pyrepl.simple_interact',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\simple_interact.py',
   'PYMODULE'),
  ('_pyrepl.trace',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\trace.py',
   'PYMODULE'),
  ('_pyrepl.types',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\types.py',
   'PYMODULE'),
  ('_pyrepl.unix_console',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\unix_console.py',
   'PYMODULE'),
  ('_pyrepl.unix_eventqueue',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\unix_eventqueue.py',
   'PYMODULE'),
  ('_pyrepl.utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\utils.py',
   'PYMODULE'),
  ('_pyrepl.windows_console',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\windows_console.py',
   'PYMODULE'),
  ('_pyrepl.windows_eventqueue',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\windows_eventqueue.py',
   'PYMODULE'),
  ('_sitebuiltins',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_sitebuiltins.py',
   'PYMODULE'),
  ('_strptime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_strptime.py',
   'PYMODULE'),
  ('_threading_local',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('argparse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\argparse.py',
   'PYMODULE'),
  ('ast',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ast.py',
   'PYMODULE'),
  ('asyncio',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.locks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.log',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.mixins',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\mixins.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.queues',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.streams',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.taskgroups',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.threads',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.timeouts',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\timeouts.py',
   'PYMODULE'),
  ('asyncio.transports',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('backports',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('base64',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\base64.py',
   'PYMODULE'),
  ('bdb',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\bdb.py',
   'PYMODULE'),
  ('bisect',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\bisect.py',
   'PYMODULE'),
  ('blinker',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\blinker\\__init__.py',
   'PYMODULE'),
  ('blinker._utilities',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\blinker\\_utilities.py',
   'PYMODULE'),
  ('blinker.base',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\blinker\\base.py',
   'PYMODULE'),
  ('bz2',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\bz2.py',
   'PYMODULE'),
  ('calendar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\calendar.py',
   'PYMODULE'),
  ('charset_normalizer',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('click',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\click\\__init__.py',
   'PYMODULE'),
  ('click._compat',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\click\\_compat.py',
   'PYMODULE'),
  ('click._termui_impl',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\click\\_termui_impl.py',
   'PYMODULE'),
  ('click._textwrap',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\click\\_textwrap.py',
   'PYMODULE'),
  ('click._winconsole',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\click\\_winconsole.py',
   'PYMODULE'),
  ('click.core',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\click\\core.py',
   'PYMODULE'),
  ('click.decorators',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\click\\decorators.py',
   'PYMODULE'),
  ('click.exceptions',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\click\\exceptions.py',
   'PYMODULE'),
  ('click.formatting',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\click\\formatting.py',
   'PYMODULE'),
  ('click.globals',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\click\\globals.py',
   'PYMODULE'),
  ('click.parser',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\click\\parser.py',
   'PYMODULE'),
  ('click.shell_completion',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\click\\shell_completion.py',
   'PYMODULE'),
  ('click.termui',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\click\\termui.py',
   'PYMODULE'),
  ('click.testing',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\click\\testing.py',
   'PYMODULE'),
  ('click.types',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\click\\types.py',
   'PYMODULE'),
  ('click.utils',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\click\\utils.py',
   'PYMODULE'),
  ('cmd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\cmd.py',
   'PYMODULE'),
  ('code',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\code.py',
   'PYMODULE'),
  ('codeop',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\codeop.py',
   'PYMODULE'),
  ('colorama',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\colorama\\__init__.py',
   'PYMODULE'),
  ('colorama.ansi',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\colorama\\ansi.py',
   'PYMODULE'),
  ('colorama.ansitowin32',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\colorama\\ansitowin32.py',
   'PYMODULE'),
  ('colorama.initialise',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\colorama\\initialise.py',
   'PYMODULE'),
  ('colorama.win32',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\colorama\\win32.py',
   'PYMODULE'),
  ('colorama.winterm',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\colorama\\winterm.py',
   'PYMODULE'),
  ('colorsys',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\colorsys.py',
   'PYMODULE'),
  ('concurrent',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('configparser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\configparser.py',
   'PYMODULE'),
  ('contextlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\contextlib.py',
   'PYMODULE'),
  ('contextvars',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\contextvars.py',
   'PYMODULE'),
  ('copy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\copy.py',
   'PYMODULE'),
  ('csv',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\csv.py',
   'PYMODULE'),
  ('ctypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._aix',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ctypes\\_aix.py',
   'PYMODULE'),
  ('ctypes._endian',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('ctypes.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ctypes\\util.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('curses',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\curses\\__init__.py',
   'PYMODULE'),
  ('curses.has_key',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\curses\\has_key.py',
   'PYMODULE'),
  ('dataclasses',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\dataclasses.py',
   'PYMODULE'),
  ('datetime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\datetime.py',
   'PYMODULE'),
  ('decimal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\decimal.py',
   'PYMODULE'),
  ('difflib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\difflib.py',
   'PYMODULE'),
  ('dis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\dis.py',
   'PYMODULE'),
  ('doctest',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\doctest.py',
   'PYMODULE'),
  ('email',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\__init__.py',
   'PYMODULE'),
  ('email._encoded_words',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\errors.py',
   'PYMODULE'),
  ('email.feedparser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\header.py',
   'PYMODULE'),
  ('email.headerregistry',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\message.py',
   'PYMODULE'),
  ('email.mime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\mime\\__init__.py',
   'PYMODULE'),
  ('email.mime.base',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\mime\\base.py',
   'PYMODULE'),
  ('email.mime.multipart',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\mime\\multipart.py',
   'PYMODULE'),
  ('email.mime.nonmultipart',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\mime\\nonmultipart.py',
   'PYMODULE'),
  ('email.mime.text',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\mime\\text.py',
   'PYMODULE'),
  ('email.parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\parser.py',
   'PYMODULE'),
  ('email.policy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\utils.py',
   'PYMODULE'),
  ('et_xmlfile',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\et_xmlfile\\__init__.py',
   'PYMODULE'),
  ('et_xmlfile.incremental_tree',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\et_xmlfile\\incremental_tree.py',
   'PYMODULE'),
  ('et_xmlfile.xmlfile',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\et_xmlfile\\xmlfile.py',
   'PYMODULE'),
  ('flask',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\flask\\__init__.py',
   'PYMODULE'),
  ('flask.__main__',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\flask\\__main__.py',
   'PYMODULE'),
  ('flask.app',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\flask\\app.py',
   'PYMODULE'),
  ('flask.blueprints',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\flask\\blueprints.py',
   'PYMODULE'),
  ('flask.cli',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\flask\\cli.py',
   'PYMODULE'),
  ('flask.config',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\flask\\config.py',
   'PYMODULE'),
  ('flask.ctx',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\flask\\ctx.py',
   'PYMODULE'),
  ('flask.debughelpers',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\flask\\debughelpers.py',
   'PYMODULE'),
  ('flask.globals',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\flask\\globals.py',
   'PYMODULE'),
  ('flask.helpers',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\flask\\helpers.py',
   'PYMODULE'),
  ('flask.json',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\flask\\json\\__init__.py',
   'PYMODULE'),
  ('flask.json.provider',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\flask\\json\\provider.py',
   'PYMODULE'),
  ('flask.json.tag',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\flask\\json\\tag.py',
   'PYMODULE'),
  ('flask.logging',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\flask\\logging.py',
   'PYMODULE'),
  ('flask.sansio', '-', 'PYMODULE'),
  ('flask.sansio.app',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\flask\\sansio\\app.py',
   'PYMODULE'),
  ('flask.sansio.blueprints',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\flask\\sansio\\blueprints.py',
   'PYMODULE'),
  ('flask.sansio.scaffold',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\flask\\sansio\\scaffold.py',
   'PYMODULE'),
  ('flask.sessions',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\flask\\sessions.py',
   'PYMODULE'),
  ('flask.signals',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\flask\\signals.py',
   'PYMODULE'),
  ('flask.templating',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\flask\\templating.py',
   'PYMODULE'),
  ('flask.testing',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\flask\\testing.py',
   'PYMODULE'),
  ('flask.typing',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\flask\\typing.py',
   'PYMODULE'),
  ('flask.views',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\flask\\views.py',
   'PYMODULE'),
  ('flask.wrappers',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\flask\\wrappers.py',
   'PYMODULE'),
  ('flask_login',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\flask_login\\__init__.py',
   'PYMODULE'),
  ('flask_login.__about__',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\flask_login\\__about__.py',
   'PYMODULE'),
  ('flask_login.config',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\flask_login\\config.py',
   'PYMODULE'),
  ('flask_login.login_manager',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\flask_login\\login_manager.py',
   'PYMODULE'),
  ('flask_login.mixins',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\flask_login\\mixins.py',
   'PYMODULE'),
  ('flask_login.signals',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\flask_login\\signals.py',
   'PYMODULE'),
  ('flask_login.test_client',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\flask_login\\test_client.py',
   'PYMODULE'),
  ('flask_login.utils',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\flask_login\\utils.py',
   'PYMODULE'),
  ('flask_sqlalchemy',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\flask_sqlalchemy\\__init__.py',
   'PYMODULE'),
  ('flask_sqlalchemy.cli',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\flask_sqlalchemy\\cli.py',
   'PYMODULE'),
  ('flask_sqlalchemy.extension',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\flask_sqlalchemy\\extension.py',
   'PYMODULE'),
  ('flask_sqlalchemy.model',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\flask_sqlalchemy\\model.py',
   'PYMODULE'),
  ('flask_sqlalchemy.pagination',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\flask_sqlalchemy\\pagination.py',
   'PYMODULE'),
  ('flask_sqlalchemy.query',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\flask_sqlalchemy\\query.py',
   'PYMODULE'),
  ('flask_sqlalchemy.record_queries',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\flask_sqlalchemy\\record_queries.py',
   'PYMODULE'),
  ('flask_sqlalchemy.session',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\flask_sqlalchemy\\session.py',
   'PYMODULE'),
  ('flask_sqlalchemy.table',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\flask_sqlalchemy\\table.py',
   'PYMODULE'),
  ('flask_sqlalchemy.track_modifications',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\flask_sqlalchemy\\track_modifications.py',
   'PYMODULE'),
  ('flask_wtf',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\flask_wtf\\__init__.py',
   'PYMODULE'),
  ('flask_wtf._compat',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\flask_wtf\\_compat.py',
   'PYMODULE'),
  ('flask_wtf.csrf',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\flask_wtf\\csrf.py',
   'PYMODULE'),
  ('flask_wtf.file',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\flask_wtf\\file.py',
   'PYMODULE'),
  ('flask_wtf.form',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\flask_wtf\\form.py',
   'PYMODULE'),
  ('flask_wtf.i18n',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\flask_wtf\\i18n.py',
   'PYMODULE'),
  ('flask_wtf.recaptcha',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\flask_wtf\\recaptcha\\__init__.py',
   'PYMODULE'),
  ('flask_wtf.recaptcha.fields',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\flask_wtf\\recaptcha\\fields.py',
   'PYMODULE'),
  ('flask_wtf.recaptcha.validators',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\flask_wtf\\recaptcha\\validators.py',
   'PYMODULE'),
  ('flask_wtf.recaptcha.widgets',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\flask_wtf\\recaptcha\\widgets.py',
   'PYMODULE'),
  ('fnmatch',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\fnmatch.py',
   'PYMODULE'),
  ('forms',
   'C:\\Users\\<USER>\\OneDrive\\Desktop\\test 111\\safaa\\forms.py',
   'PYMODULE'),
  ('fractions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\fractions.py',
   'PYMODULE'),
  ('ftplib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ftplib.py',
   'PYMODULE'),
  ('getopt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\getopt.py',
   'PYMODULE'),
  ('getpass',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\getpass.py',
   'PYMODULE'),
  ('gettext',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\gettext.py',
   'PYMODULE'),
  ('glob',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\glob.py',
   'PYMODULE'),
  ('greenlet',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\greenlet\\__init__.py',
   'PYMODULE'),
  ('gzip',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\gzip.py',
   'PYMODULE'),
  ('hashlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\hashlib.py',
   'PYMODULE'),
  ('hmac',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\hmac.py',
   'PYMODULE'),
  ('html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\html\\__init__.py',
   'PYMODULE'),
  ('html.entities',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\html\\entities.py',
   'PYMODULE'),
  ('html.parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\html\\parser.py',
   'PYMODULE'),
  ('http',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\http\\__init__.py',
   'PYMODULE'),
  ('http.client',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\http\\client.py',
   'PYMODULE'),
  ('http.cookiejar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http.server',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\http\\server.py',
   'PYMODULE'),
  ('importlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._functional',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\resources\\_functional.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\util.py',
   'PYMODULE'),
  ('inspect',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\inspect.py',
   'PYMODULE'),
  ('ipaddress',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ipaddress.py',
   'PYMODULE'),
  ('itsdangerous',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\itsdangerous\\__init__.py',
   'PYMODULE'),
  ('itsdangerous._json',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\itsdangerous\\_json.py',
   'PYMODULE'),
  ('itsdangerous.encoding',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\itsdangerous\\encoding.py',
   'PYMODULE'),
  ('itsdangerous.exc',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\itsdangerous\\exc.py',
   'PYMODULE'),
  ('itsdangerous.serializer',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\itsdangerous\\serializer.py',
   'PYMODULE'),
  ('itsdangerous.signer',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\itsdangerous\\signer.py',
   'PYMODULE'),
  ('itsdangerous.timed',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\itsdangerous\\timed.py',
   'PYMODULE'),
  ('itsdangerous.url_safe',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\itsdangerous\\url_safe.py',
   'PYMODULE'),
  ('jaraco', '-', 'PYMODULE'),
  ('jinja2',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\jinja2\\__init__.py',
   'PYMODULE'),
  ('jinja2._identifier',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\jinja2\\_identifier.py',
   'PYMODULE'),
  ('jinja2.async_utils',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\jinja2\\async_utils.py',
   'PYMODULE'),
  ('jinja2.bccache',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\jinja2\\bccache.py',
   'PYMODULE'),
  ('jinja2.compiler',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\jinja2\\compiler.py',
   'PYMODULE'),
  ('jinja2.constants',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\jinja2\\constants.py',
   'PYMODULE'),
  ('jinja2.debug',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\jinja2\\debug.py',
   'PYMODULE'),
  ('jinja2.defaults',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\jinja2\\defaults.py',
   'PYMODULE'),
  ('jinja2.environment',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\jinja2\\environment.py',
   'PYMODULE'),
  ('jinja2.exceptions',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\jinja2\\exceptions.py',
   'PYMODULE'),
  ('jinja2.ext',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\jinja2\\ext.py',
   'PYMODULE'),
  ('jinja2.filters',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\jinja2\\filters.py',
   'PYMODULE'),
  ('jinja2.idtracking',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\jinja2\\idtracking.py',
   'PYMODULE'),
  ('jinja2.lexer',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\jinja2\\lexer.py',
   'PYMODULE'),
  ('jinja2.loaders',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\jinja2\\loaders.py',
   'PYMODULE'),
  ('jinja2.nodes',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\jinja2\\nodes.py',
   'PYMODULE'),
  ('jinja2.optimizer',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\jinja2\\optimizer.py',
   'PYMODULE'),
  ('jinja2.parser',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\jinja2\\parser.py',
   'PYMODULE'),
  ('jinja2.runtime',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\jinja2\\runtime.py',
   'PYMODULE'),
  ('jinja2.sandbox',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\jinja2\\sandbox.py',
   'PYMODULE'),
  ('jinja2.tests',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\jinja2\\tests.py',
   'PYMODULE'),
  ('jinja2.utils',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\jinja2\\utils.py',
   'PYMODULE'),
  ('jinja2.visitor',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\jinja2\\visitor.py',
   'PYMODULE'),
  ('json',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\json\\__init__.py',
   'PYMODULE'),
  ('json.decoder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.encoder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.scanner',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\json\\scanner.py',
   'PYMODULE'),
  ('logging',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\logging\\__init__.py',
   'PYMODULE'),
  ('lzma',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\lzma.py',
   'PYMODULE'),
  ('markupsafe',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\markupsafe\\__init__.py',
   'PYMODULE'),
  ('markupsafe._native',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\markupsafe\\_native.py',
   'PYMODULE'),
  ('mimetypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\mimetypes.py',
   'PYMODULE'),
  ('models',
   'C:\\Users\\<USER>\\OneDrive\\Desktop\\test 111\\safaa\\models.py',
   'PYMODULE'),
  ('multiprocessing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('netrc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\netrc.py',
   'PYMODULE'),
  ('nturl2path',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\nturl2path.py',
   'PYMODULE'),
  ('numbers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\numbers.py',
   'PYMODULE'),
  ('opcode',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\opcode.py',
   'PYMODULE'),
  ('openpyxl',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\__init__.py',
   'PYMODULE'),
  ('openpyxl._constants',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\_constants.py',
   'PYMODULE'),
  ('openpyxl.cell',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\cell\\__init__.py',
   'PYMODULE'),
  ('openpyxl.cell._writer',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\cell\\_writer.py',
   'PYMODULE'),
  ('openpyxl.cell.cell',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\cell\\cell.py',
   'PYMODULE'),
  ('openpyxl.cell.read_only',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\cell\\read_only.py',
   'PYMODULE'),
  ('openpyxl.cell.rich_text',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\cell\\rich_text.py',
   'PYMODULE'),
  ('openpyxl.cell.text',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\cell\\text.py',
   'PYMODULE'),
  ('openpyxl.chart',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\chart\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chart._3d',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\chart\\_3d.py',
   'PYMODULE'),
  ('openpyxl.chart._chart',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\chart\\_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.area_chart',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\chart\\area_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.axis',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\chart\\axis.py',
   'PYMODULE'),
  ('openpyxl.chart.bar_chart',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\chart\\bar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.bubble_chart',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\chart\\bubble_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.chartspace',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\chart\\chartspace.py',
   'PYMODULE'),
  ('openpyxl.chart.data_source',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\chart\\data_source.py',
   'PYMODULE'),
  ('openpyxl.chart.descriptors',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\chart\\descriptors.py',
   'PYMODULE'),
  ('openpyxl.chart.error_bar',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\chart\\error_bar.py',
   'PYMODULE'),
  ('openpyxl.chart.label',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\chart\\label.py',
   'PYMODULE'),
  ('openpyxl.chart.layout',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\chart\\layout.py',
   'PYMODULE'),
  ('openpyxl.chart.legend',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\chart\\legend.py',
   'PYMODULE'),
  ('openpyxl.chart.line_chart',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\chart\\line_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.marker',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\chart\\marker.py',
   'PYMODULE'),
  ('openpyxl.chart.picture',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\chart\\picture.py',
   'PYMODULE'),
  ('openpyxl.chart.pie_chart',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\chart\\pie_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.pivot',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\chart\\pivot.py',
   'PYMODULE'),
  ('openpyxl.chart.plotarea',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\chart\\plotarea.py',
   'PYMODULE'),
  ('openpyxl.chart.print_settings',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\chart\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.chart.radar_chart',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\chart\\radar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.reader',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\chart\\reader.py',
   'PYMODULE'),
  ('openpyxl.chart.reference',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\chart\\reference.py',
   'PYMODULE'),
  ('openpyxl.chart.scatter_chart',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\chart\\scatter_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.series',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\chart\\series.py',
   'PYMODULE'),
  ('openpyxl.chart.series_factory',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\chart\\series_factory.py',
   'PYMODULE'),
  ('openpyxl.chart.shapes',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\chart\\shapes.py',
   'PYMODULE'),
  ('openpyxl.chart.stock_chart',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\chart\\stock_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.surface_chart',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\chart\\surface_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.text',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\chart\\text.py',
   'PYMODULE'),
  ('openpyxl.chart.title',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\chart\\title.py',
   'PYMODULE'),
  ('openpyxl.chart.trendline',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\chart\\trendline.py',
   'PYMODULE'),
  ('openpyxl.chart.updown_bars',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\chart\\updown_bars.py',
   'PYMODULE'),
  ('openpyxl.chartsheet',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\chartsheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.chartsheet',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\chartsheet\\chartsheet.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.custom',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\chartsheet\\custom.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.properties',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\chartsheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.protection',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\chartsheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.publish',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\chartsheet\\publish.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.relation',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\chartsheet\\relation.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.views',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\chartsheet\\views.py',
   'PYMODULE'),
  ('openpyxl.comments',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\comments\\__init__.py',
   'PYMODULE'),
  ('openpyxl.comments.author',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\comments\\author.py',
   'PYMODULE'),
  ('openpyxl.comments.comment_sheet',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\comments\\comment_sheet.py',
   'PYMODULE'),
  ('openpyxl.comments.comments',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\comments\\comments.py',
   'PYMODULE'),
  ('openpyxl.comments.shape_writer',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\comments\\shape_writer.py',
   'PYMODULE'),
  ('openpyxl.compat',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\compat\\__init__.py',
   'PYMODULE'),
  ('openpyxl.compat.abc',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\compat\\abc.py',
   'PYMODULE'),
  ('openpyxl.compat.numbers',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\compat\\numbers.py',
   'PYMODULE'),
  ('openpyxl.compat.product',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\compat\\product.py',
   'PYMODULE'),
  ('openpyxl.compat.singleton',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\compat\\singleton.py',
   'PYMODULE'),
  ('openpyxl.compat.strings',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\compat\\strings.py',
   'PYMODULE'),
  ('openpyxl.descriptors',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\descriptors\\__init__.py',
   'PYMODULE'),
  ('openpyxl.descriptors.base',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\descriptors\\base.py',
   'PYMODULE'),
  ('openpyxl.descriptors.container',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\descriptors\\container.py',
   'PYMODULE'),
  ('openpyxl.descriptors.excel',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\descriptors\\excel.py',
   'PYMODULE'),
  ('openpyxl.descriptors.namespace',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\descriptors\\namespace.py',
   'PYMODULE'),
  ('openpyxl.descriptors.nested',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\descriptors\\nested.py',
   'PYMODULE'),
  ('openpyxl.descriptors.sequence',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\descriptors\\sequence.py',
   'PYMODULE'),
  ('openpyxl.descriptors.serialisable',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\descriptors\\serialisable.py',
   'PYMODULE'),
  ('openpyxl.descriptors.slots',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\descriptors\\slots.py',
   'PYMODULE'),
  ('openpyxl.drawing',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\__init__.py',
   'PYMODULE'),
  ('openpyxl.drawing.colors',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\colors.py',
   'PYMODULE'),
  ('openpyxl.drawing.connector',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\connector.py',
   'PYMODULE'),
  ('openpyxl.drawing.drawing',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.effect',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\effect.py',
   'PYMODULE'),
  ('openpyxl.drawing.fill',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\fill.py',
   'PYMODULE'),
  ('openpyxl.drawing.geometry',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\geometry.py',
   'PYMODULE'),
  ('openpyxl.drawing.graphic',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\graphic.py',
   'PYMODULE'),
  ('openpyxl.drawing.image',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\image.py',
   'PYMODULE'),
  ('openpyxl.drawing.line',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\line.py',
   'PYMODULE'),
  ('openpyxl.drawing.picture',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\picture.py',
   'PYMODULE'),
  ('openpyxl.drawing.properties',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\properties.py',
   'PYMODULE'),
  ('openpyxl.drawing.relation',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\relation.py',
   'PYMODULE'),
  ('openpyxl.drawing.spreadsheet_drawing',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\spreadsheet_drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.text',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\text.py',
   'PYMODULE'),
  ('openpyxl.drawing.xdr',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\xdr.py',
   'PYMODULE'),
  ('openpyxl.formatting',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\formatting\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formatting.formatting',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\formatting\\formatting.py',
   'PYMODULE'),
  ('openpyxl.formatting.rule',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\formatting\\rule.py',
   'PYMODULE'),
  ('openpyxl.formula',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\formula\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formula.tokenizer',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\formula\\tokenizer.py',
   'PYMODULE'),
  ('openpyxl.formula.translate',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\formula\\translate.py',
   'PYMODULE'),
  ('openpyxl.packaging',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\packaging\\__init__.py',
   'PYMODULE'),
  ('openpyxl.packaging.core',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\packaging\\core.py',
   'PYMODULE'),
  ('openpyxl.packaging.custom',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\packaging\\custom.py',
   'PYMODULE'),
  ('openpyxl.packaging.extended',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\packaging\\extended.py',
   'PYMODULE'),
  ('openpyxl.packaging.interface',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\packaging\\interface.py',
   'PYMODULE'),
  ('openpyxl.packaging.manifest',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\packaging\\manifest.py',
   'PYMODULE'),
  ('openpyxl.packaging.relationship',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\packaging\\relationship.py',
   'PYMODULE'),
  ('openpyxl.packaging.workbook',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\packaging\\workbook.py',
   'PYMODULE'),
  ('openpyxl.pivot',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\pivot\\__init__.py',
   'PYMODULE'),
  ('openpyxl.pivot.cache',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\pivot\\cache.py',
   'PYMODULE'),
  ('openpyxl.pivot.fields',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\pivot\\fields.py',
   'PYMODULE'),
  ('openpyxl.pivot.record',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\pivot\\record.py',
   'PYMODULE'),
  ('openpyxl.pivot.table',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\pivot\\table.py',
   'PYMODULE'),
  ('openpyxl.reader',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\reader\\__init__.py',
   'PYMODULE'),
  ('openpyxl.reader.drawings',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\reader\\drawings.py',
   'PYMODULE'),
  ('openpyxl.reader.excel',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\reader\\excel.py',
   'PYMODULE'),
  ('openpyxl.reader.strings',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\reader\\strings.py',
   'PYMODULE'),
  ('openpyxl.reader.workbook',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\reader\\workbook.py',
   'PYMODULE'),
  ('openpyxl.styles',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\styles\\__init__.py',
   'PYMODULE'),
  ('openpyxl.styles.alignment',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\styles\\alignment.py',
   'PYMODULE'),
  ('openpyxl.styles.borders',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\styles\\borders.py',
   'PYMODULE'),
  ('openpyxl.styles.builtins',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\styles\\builtins.py',
   'PYMODULE'),
  ('openpyxl.styles.cell_style',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\styles\\cell_style.py',
   'PYMODULE'),
  ('openpyxl.styles.colors',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\styles\\colors.py',
   'PYMODULE'),
  ('openpyxl.styles.differential',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\styles\\differential.py',
   'PYMODULE'),
  ('openpyxl.styles.fills',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\styles\\fills.py',
   'PYMODULE'),
  ('openpyxl.styles.fonts',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\styles\\fonts.py',
   'PYMODULE'),
  ('openpyxl.styles.named_styles',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\styles\\named_styles.py',
   'PYMODULE'),
  ('openpyxl.styles.numbers',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\styles\\numbers.py',
   'PYMODULE'),
  ('openpyxl.styles.protection',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\styles\\protection.py',
   'PYMODULE'),
  ('openpyxl.styles.proxy',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\styles\\proxy.py',
   'PYMODULE'),
  ('openpyxl.styles.styleable',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\styles\\styleable.py',
   'PYMODULE'),
  ('openpyxl.styles.stylesheet',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\styles\\stylesheet.py',
   'PYMODULE'),
  ('openpyxl.styles.table',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\styles\\table.py',
   'PYMODULE'),
  ('openpyxl.utils',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\utils\\__init__.py',
   'PYMODULE'),
  ('openpyxl.utils.bound_dictionary',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\utils\\bound_dictionary.py',
   'PYMODULE'),
  ('openpyxl.utils.cell',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\utils\\cell.py',
   'PYMODULE'),
  ('openpyxl.utils.dataframe',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\utils\\dataframe.py',
   'PYMODULE'),
  ('openpyxl.utils.datetime',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\utils\\datetime.py',
   'PYMODULE'),
  ('openpyxl.utils.escape',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\utils\\escape.py',
   'PYMODULE'),
  ('openpyxl.utils.exceptions',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\utils\\exceptions.py',
   'PYMODULE'),
  ('openpyxl.utils.formulas',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\utils\\formulas.py',
   'PYMODULE'),
  ('openpyxl.utils.indexed_list',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\utils\\indexed_list.py',
   'PYMODULE'),
  ('openpyxl.utils.inference',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\utils\\inference.py',
   'PYMODULE'),
  ('openpyxl.utils.protection',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\utils\\protection.py',
   'PYMODULE'),
  ('openpyxl.utils.units',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\utils\\units.py',
   'PYMODULE'),
  ('openpyxl.workbook',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\workbook\\__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook._writer',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\workbook\\_writer.py',
   'PYMODULE'),
  ('openpyxl.workbook.child',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\workbook\\child.py',
   'PYMODULE'),
  ('openpyxl.workbook.defined_name',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\workbook\\defined_name.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\workbook\\external_link\\__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link.external',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\workbook\\external_link\\external.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_reference',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\workbook\\external_reference.py',
   'PYMODULE'),
  ('openpyxl.workbook.function_group',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\workbook\\function_group.py',
   'PYMODULE'),
  ('openpyxl.workbook.properties',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\workbook\\properties.py',
   'PYMODULE'),
  ('openpyxl.workbook.protection',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\workbook\\protection.py',
   'PYMODULE'),
  ('openpyxl.workbook.smart_tags',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\workbook\\smart_tags.py',
   'PYMODULE'),
  ('openpyxl.workbook.views',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\workbook\\views.py',
   'PYMODULE'),
  ('openpyxl.workbook.web',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\workbook\\web.py',
   'PYMODULE'),
  ('openpyxl.workbook.workbook',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\workbook\\workbook.py',
   'PYMODULE'),
  ('openpyxl.worksheet',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.worksheet._read_only',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\_read_only.py',
   'PYMODULE'),
  ('openpyxl.worksheet._reader',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\_reader.py',
   'PYMODULE'),
  ('openpyxl.worksheet._write_only',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\_write_only.py',
   'PYMODULE'),
  ('openpyxl.worksheet._writer',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\_writer.py',
   'PYMODULE'),
  ('openpyxl.worksheet.cell_range',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\cell_range.py',
   'PYMODULE'),
  ('openpyxl.worksheet.cell_watch',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\cell_watch.py',
   'PYMODULE'),
  ('openpyxl.worksheet.controls',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\controls.py',
   'PYMODULE'),
  ('openpyxl.worksheet.copier',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\copier.py',
   'PYMODULE'),
  ('openpyxl.worksheet.custom',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\custom.py',
   'PYMODULE'),
  ('openpyxl.worksheet.datavalidation',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\datavalidation.py',
   'PYMODULE'),
  ('openpyxl.worksheet.dimensions',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\dimensions.py',
   'PYMODULE'),
  ('openpyxl.worksheet.drawing',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\drawing.py',
   'PYMODULE'),
  ('openpyxl.worksheet.errors',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\errors.py',
   'PYMODULE'),
  ('openpyxl.worksheet.filters',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\filters.py',
   'PYMODULE'),
  ('openpyxl.worksheet.formula',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\formula.py',
   'PYMODULE'),
  ('openpyxl.worksheet.header_footer',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\header_footer.py',
   'PYMODULE'),
  ('openpyxl.worksheet.hyperlink',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\hyperlink.py',
   'PYMODULE'),
  ('openpyxl.worksheet.merge',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\merge.py',
   'PYMODULE'),
  ('openpyxl.worksheet.ole',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\ole.py',
   'PYMODULE'),
  ('openpyxl.worksheet.page',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\page.py',
   'PYMODULE'),
  ('openpyxl.worksheet.pagebreak',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\pagebreak.py',
   'PYMODULE'),
  ('openpyxl.worksheet.picture',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\picture.py',
   'PYMODULE'),
  ('openpyxl.worksheet.print_settings',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.worksheet.properties',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.worksheet.protection',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.worksheet.related',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\related.py',
   'PYMODULE'),
  ('openpyxl.worksheet.scenario',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\scenario.py',
   'PYMODULE'),
  ('openpyxl.worksheet.smart_tag',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\smart_tag.py',
   'PYMODULE'),
  ('openpyxl.worksheet.table',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\table.py',
   'PYMODULE'),
  ('openpyxl.worksheet.views',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\views.py',
   'PYMODULE'),
  ('openpyxl.worksheet.worksheet',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\worksheet.py',
   'PYMODULE'),
  ('openpyxl.writer',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\writer\\__init__.py',
   'PYMODULE'),
  ('openpyxl.writer.excel',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\writer\\excel.py',
   'PYMODULE'),
  ('openpyxl.writer.theme',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\writer\\theme.py',
   'PYMODULE'),
  ('openpyxl.xml',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\xml\\__init__.py',
   'PYMODULE'),
  ('openpyxl.xml.constants',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\xml\\constants.py',
   'PYMODULE'),
  ('openpyxl.xml.functions',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\openpyxl\\xml\\functions.py',
   'PYMODULE'),
  ('packaging',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('packaging._elffile',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._parser',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('packaging._structures',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging.licenses',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\packaging\\licenses\\__init__.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\packaging\\licenses\\_spdx.py',
   'PYMODULE'),
  ('packaging.markers',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('packaging.requirements',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.tags',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('packaging.utils',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging.version',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('pathlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\pathlib\\__init__.py',
   'PYMODULE'),
  ('pathlib._abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\pathlib\\_abc.py',
   'PYMODULE'),
  ('pathlib._local',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\pathlib\\_local.py',
   'PYMODULE'),
  ('pdb',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\pdb.py',
   'PYMODULE'),
  ('pickle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\pickle.py',
   'PYMODULE'),
  ('pkgutil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\pkgutil.py',
   'PYMODULE'),
  ('platform',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\platform.py',
   'PYMODULE'),
  ('pprint',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\pprint.py',
   'PYMODULE'),
  ('py_compile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\py_compile.py',
   'PYMODULE'),
  ('pydoc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\pydoc.py',
   'PYMODULE'),
  ('pydoc_data',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('queue',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\queue.py',
   'PYMODULE'),
  ('quopri',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\quopri.py',
   'PYMODULE'),
  ('random',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\random.py',
   'PYMODULE'),
  ('reportlab',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\__init__.py',
   'PYMODULE'),
  ('reportlab.graphics',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\graphics\\__init__.py',
   'PYMODULE'),
  ('reportlab.graphics.barcode',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\graphics\\barcode\\__init__.py',
   'PYMODULE'),
  ('reportlab.graphics.barcode.code128',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\graphics\\barcode\\code128.py',
   'PYMODULE'),
  ('reportlab.graphics.barcode.code39',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\graphics\\barcode\\code39.py',
   'PYMODULE'),
  ('reportlab.graphics.barcode.code93',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\graphics\\barcode\\code93.py',
   'PYMODULE'),
  ('reportlab.graphics.barcode.common',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\graphics\\barcode\\common.py',
   'PYMODULE'),
  ('reportlab.graphics.barcode.dmtx',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\graphics\\barcode\\dmtx.py',
   'PYMODULE'),
  ('reportlab.graphics.barcode.eanbc',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\graphics\\barcode\\eanbc.py',
   'PYMODULE'),
  ('reportlab.graphics.barcode.ecc200datamatrix',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\graphics\\barcode\\ecc200datamatrix.py',
   'PYMODULE'),
  ('reportlab.graphics.barcode.fourstate',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\graphics\\barcode\\fourstate.py',
   'PYMODULE'),
  ('reportlab.graphics.barcode.lto',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\graphics\\barcode\\lto.py',
   'PYMODULE'),
  ('reportlab.graphics.barcode.qr',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\graphics\\barcode\\qr.py',
   'PYMODULE'),
  ('reportlab.graphics.barcode.qrencoder',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\graphics\\barcode\\qrencoder.py',
   'PYMODULE'),
  ('reportlab.graphics.barcode.test',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\graphics\\barcode\\test.py',
   'PYMODULE'),
  ('reportlab.graphics.barcode.usps',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\graphics\\barcode\\usps.py',
   'PYMODULE'),
  ('reportlab.graphics.barcode.usps4s',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\graphics\\barcode\\usps4s.py',
   'PYMODULE'),
  ('reportlab.graphics.barcode.widgets',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\graphics\\barcode\\widgets.py',
   'PYMODULE'),
  ('reportlab.graphics.charts',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\graphics\\charts\\__init__.py',
   'PYMODULE'),
  ('reportlab.graphics.charts.areas',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\graphics\\charts\\areas.py',
   'PYMODULE'),
  ('reportlab.graphics.charts.axes',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\graphics\\charts\\axes.py',
   'PYMODULE'),
  ('reportlab.graphics.charts.barcharts',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\graphics\\charts\\barcharts.py',
   'PYMODULE'),
  ('reportlab.graphics.charts.dotbox',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\graphics\\charts\\dotbox.py',
   'PYMODULE'),
  ('reportlab.graphics.charts.doughnut',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\graphics\\charts\\doughnut.py',
   'PYMODULE'),
  ('reportlab.graphics.charts.legends',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\graphics\\charts\\legends.py',
   'PYMODULE'),
  ('reportlab.graphics.charts.linecharts',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\graphics\\charts\\linecharts.py',
   'PYMODULE'),
  ('reportlab.graphics.charts.lineplots',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\graphics\\charts\\lineplots.py',
   'PYMODULE'),
  ('reportlab.graphics.charts.markers',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\graphics\\charts\\markers.py',
   'PYMODULE'),
  ('reportlab.graphics.charts.piecharts',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\graphics\\charts\\piecharts.py',
   'PYMODULE'),
  ('reportlab.graphics.charts.slidebox',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\graphics\\charts\\slidebox.py',
   'PYMODULE'),
  ('reportlab.graphics.charts.spider',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\graphics\\charts\\spider.py',
   'PYMODULE'),
  ('reportlab.graphics.charts.textlabels',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\graphics\\charts\\textlabels.py',
   'PYMODULE'),
  ('reportlab.graphics.charts.utils',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\graphics\\charts\\utils.py',
   'PYMODULE'),
  ('reportlab.graphics.charts.utils3d',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\graphics\\charts\\utils3d.py',
   'PYMODULE'),
  ('reportlab.graphics.renderPDF',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\graphics\\renderPDF.py',
   'PYMODULE'),
  ('reportlab.graphics.renderPM',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\graphics\\renderPM.py',
   'PYMODULE'),
  ('reportlab.graphics.renderPS',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\graphics\\renderPS.py',
   'PYMODULE'),
  ('reportlab.graphics.renderSVG',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\graphics\\renderSVG.py',
   'PYMODULE'),
  ('reportlab.graphics.renderbase',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\graphics\\renderbase.py',
   'PYMODULE'),
  ('reportlab.graphics.samples',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\graphics\\samples\\__init__.py',
   'PYMODULE'),
  ('reportlab.graphics.samples.bubble',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\graphics\\samples\\bubble.py',
   'PYMODULE'),
  ('reportlab.graphics.samples.clustered_bar',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\graphics\\samples\\clustered_bar.py',
   'PYMODULE'),
  ('reportlab.graphics.samples.clustered_column',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\graphics\\samples\\clustered_column.py',
   'PYMODULE'),
  ('reportlab.graphics.samples.excelcolors',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\graphics\\samples\\excelcolors.py',
   'PYMODULE'),
  ('reportlab.graphics.samples.exploded_pie',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\graphics\\samples\\exploded_pie.py',
   'PYMODULE'),
  ('reportlab.graphics.samples.filled_radar',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\graphics\\samples\\filled_radar.py',
   'PYMODULE'),
  ('reportlab.graphics.samples.line_chart',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\graphics\\samples\\line_chart.py',
   'PYMODULE'),
  ('reportlab.graphics.samples.linechart_with_markers',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\graphics\\samples\\linechart_with_markers.py',
   'PYMODULE'),
  ('reportlab.graphics.samples.radar',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\graphics\\samples\\radar.py',
   'PYMODULE'),
  ('reportlab.graphics.samples.runall',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\graphics\\samples\\runall.py',
   'PYMODULE'),
  ('reportlab.graphics.samples.scatter',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\graphics\\samples\\scatter.py',
   'PYMODULE'),
  ('reportlab.graphics.samples.scatter_lines',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\graphics\\samples\\scatter_lines.py',
   'PYMODULE'),
  ('reportlab.graphics.samples.scatter_lines_markers',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\graphics\\samples\\scatter_lines_markers.py',
   'PYMODULE'),
  ('reportlab.graphics.samples.simple_pie',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\graphics\\samples\\simple_pie.py',
   'PYMODULE'),
  ('reportlab.graphics.samples.stacked_bar',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\graphics\\samples\\stacked_bar.py',
   'PYMODULE'),
  ('reportlab.graphics.samples.stacked_column',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\graphics\\samples\\stacked_column.py',
   'PYMODULE'),
  ('reportlab.graphics.shapes',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\graphics\\shapes.py',
   'PYMODULE'),
  ('reportlab.graphics.svgpath',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\graphics\\svgpath.py',
   'PYMODULE'),
  ('reportlab.graphics.testdrawings',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\graphics\\testdrawings.py',
   'PYMODULE'),
  ('reportlab.graphics.testshapes',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\graphics\\testshapes.py',
   'PYMODULE'),
  ('reportlab.graphics.transform',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\graphics\\transform.py',
   'PYMODULE'),
  ('reportlab.graphics.utils',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\graphics\\utils.py',
   'PYMODULE'),
  ('reportlab.graphics.widgetbase',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\graphics\\widgetbase.py',
   'PYMODULE'),
  ('reportlab.graphics.widgets',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\graphics\\widgets\\__init__.py',
   'PYMODULE'),
  ('reportlab.graphics.widgets.adjustableArrow',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\graphics\\widgets\\adjustableArrow.py',
   'PYMODULE'),
  ('reportlab.graphics.widgets.eventcal',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\graphics\\widgets\\eventcal.py',
   'PYMODULE'),
  ('reportlab.graphics.widgets.flags',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\graphics\\widgets\\flags.py',
   'PYMODULE'),
  ('reportlab.graphics.widgets.grids',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\graphics\\widgets\\grids.py',
   'PYMODULE'),
  ('reportlab.graphics.widgets.markers',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\graphics\\widgets\\markers.py',
   'PYMODULE'),
  ('reportlab.graphics.widgets.signsandsymbols',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\graphics\\widgets\\signsandsymbols.py',
   'PYMODULE'),
  ('reportlab.graphics.widgets.table',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\graphics\\widgets\\table.py',
   'PYMODULE'),
  ('reportlab.lib',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\lib\\__init__.py',
   'PYMODULE'),
  ('reportlab.lib.PyFontify',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\lib\\PyFontify.py',
   'PYMODULE'),
  ('reportlab.lib.abag',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\lib\\abag.py',
   'PYMODULE'),
  ('reportlab.lib.arciv',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\lib\\arciv.py',
   'PYMODULE'),
  ('reportlab.lib.attrmap',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\lib\\attrmap.py',
   'PYMODULE'),
  ('reportlab.lib.boxstuff',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\lib\\boxstuff.py',
   'PYMODULE'),
  ('reportlab.lib.codecharts',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\lib\\codecharts.py',
   'PYMODULE'),
  ('reportlab.lib.colors',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\lib\\colors.py',
   'PYMODULE'),
  ('reportlab.lib.corp',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\lib\\corp.py',
   'PYMODULE'),
  ('reportlab.lib.enums',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\lib\\enums.py',
   'PYMODULE'),
  ('reportlab.lib.extformat',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\lib\\extformat.py',
   'PYMODULE'),
  ('reportlab.lib.fontfinder',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\lib\\fontfinder.py',
   'PYMODULE'),
  ('reportlab.lib.fonts',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\lib\\fonts.py',
   'PYMODULE'),
  ('reportlab.lib.formatters',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\lib\\formatters.py',
   'PYMODULE'),
  ('reportlab.lib.geomutils',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\lib\\geomutils.py',
   'PYMODULE'),
  ('reportlab.lib.logger',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\lib\\logger.py',
   'PYMODULE'),
  ('reportlab.lib.normalDate',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\lib\\normalDate.py',
   'PYMODULE'),
  ('reportlab.lib.pagesizes',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\lib\\pagesizes.py',
   'PYMODULE'),
  ('reportlab.lib.pdfencrypt',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\lib\\pdfencrypt.py',
   'PYMODULE'),
  ('reportlab.lib.pygments2xpre',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\lib\\pygments2xpre.py',
   'PYMODULE'),
  ('reportlab.lib.randomtext',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\lib\\randomtext.py',
   'PYMODULE'),
  ('reportlab.lib.rl_accel',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\lib\\rl_accel.py',
   'PYMODULE'),
  ('reportlab.lib.rl_safe_eval',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\lib\\rl_safe_eval.py',
   'PYMODULE'),
  ('reportlab.lib.rltempfile',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\lib\\rltempfile.py',
   'PYMODULE'),
  ('reportlab.lib.rparsexml',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\lib\\rparsexml.py',
   'PYMODULE'),
  ('reportlab.lib.sequencer',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\lib\\sequencer.py',
   'PYMODULE'),
  ('reportlab.lib.styles',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\lib\\styles.py',
   'PYMODULE'),
  ('reportlab.lib.testutils',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\lib\\testutils.py',
   'PYMODULE'),
  ('reportlab.lib.textsplit',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\lib\\textsplit.py',
   'PYMODULE'),
  ('reportlab.lib.units',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\lib\\units.py',
   'PYMODULE'),
  ('reportlab.lib.utils',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\lib\\utils.py',
   'PYMODULE'),
  ('reportlab.lib.validators',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\lib\\validators.py',
   'PYMODULE'),
  ('reportlab.lib.yaml',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\lib\\yaml.py',
   'PYMODULE'),
  ('reportlab.pdfbase',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\pdfbase\\__init__.py',
   'PYMODULE'),
  ('reportlab.pdfbase._can_cmap_data',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\pdfbase\\_can_cmap_data.py',
   'PYMODULE'),
  ('reportlab.pdfbase._cidfontdata',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\pdfbase\\_cidfontdata.py',
   'PYMODULE'),
  ('reportlab.pdfbase._fontdata',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\pdfbase\\_fontdata.py',
   'PYMODULE'),
  ('reportlab.pdfbase._fontdata_enc_macexpert',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\pdfbase\\_fontdata_enc_macexpert.py',
   'PYMODULE'),
  ('reportlab.pdfbase._fontdata_enc_macroman',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\pdfbase\\_fontdata_enc_macroman.py',
   'PYMODULE'),
  ('reportlab.pdfbase._fontdata_enc_pdfdoc',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\pdfbase\\_fontdata_enc_pdfdoc.py',
   'PYMODULE'),
  ('reportlab.pdfbase._fontdata_enc_standard',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\pdfbase\\_fontdata_enc_standard.py',
   'PYMODULE'),
  ('reportlab.pdfbase._fontdata_enc_symbol',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\pdfbase\\_fontdata_enc_symbol.py',
   'PYMODULE'),
  ('reportlab.pdfbase._fontdata_enc_winansi',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\pdfbase\\_fontdata_enc_winansi.py',
   'PYMODULE'),
  ('reportlab.pdfbase._fontdata_enc_zapfdingbats',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\pdfbase\\_fontdata_enc_zapfdingbats.py',
   'PYMODULE'),
  ('reportlab.pdfbase._fontdata_widths_courier',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\pdfbase\\_fontdata_widths_courier.py',
   'PYMODULE'),
  ('reportlab.pdfbase._fontdata_widths_courierbold',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\pdfbase\\_fontdata_widths_courierbold.py',
   'PYMODULE'),
  ('reportlab.pdfbase._fontdata_widths_courierboldoblique',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\pdfbase\\_fontdata_widths_courierboldoblique.py',
   'PYMODULE'),
  ('reportlab.pdfbase._fontdata_widths_courieroblique',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\pdfbase\\_fontdata_widths_courieroblique.py',
   'PYMODULE'),
  ('reportlab.pdfbase._fontdata_widths_helvetica',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\pdfbase\\_fontdata_widths_helvetica.py',
   'PYMODULE'),
  ('reportlab.pdfbase._fontdata_widths_helveticabold',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\pdfbase\\_fontdata_widths_helveticabold.py',
   'PYMODULE'),
  ('reportlab.pdfbase._fontdata_widths_helveticaboldoblique',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\pdfbase\\_fontdata_widths_helveticaboldoblique.py',
   'PYMODULE'),
  ('reportlab.pdfbase._fontdata_widths_helveticaoblique',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\pdfbase\\_fontdata_widths_helveticaoblique.py',
   'PYMODULE'),
  ('reportlab.pdfbase._fontdata_widths_symbol',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\pdfbase\\_fontdata_widths_symbol.py',
   'PYMODULE'),
  ('reportlab.pdfbase._fontdata_widths_timesbold',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\pdfbase\\_fontdata_widths_timesbold.py',
   'PYMODULE'),
  ('reportlab.pdfbase._fontdata_widths_timesbolditalic',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\pdfbase\\_fontdata_widths_timesbolditalic.py',
   'PYMODULE'),
  ('reportlab.pdfbase._fontdata_widths_timesitalic',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\pdfbase\\_fontdata_widths_timesitalic.py',
   'PYMODULE'),
  ('reportlab.pdfbase._fontdata_widths_timesroman',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\pdfbase\\_fontdata_widths_timesroman.py',
   'PYMODULE'),
  ('reportlab.pdfbase._fontdata_widths_zapfdingbats',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\pdfbase\\_fontdata_widths_zapfdingbats.py',
   'PYMODULE'),
  ('reportlab.pdfbase._glyphlist',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\pdfbase\\_glyphlist.py',
   'PYMODULE'),
  ('reportlab.pdfbase.acroform',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\pdfbase\\acroform.py',
   'PYMODULE'),
  ('reportlab.pdfbase.cidfonts',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\pdfbase\\cidfonts.py',
   'PYMODULE'),
  ('reportlab.pdfbase.pdfdoc',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\pdfbase\\pdfdoc.py',
   'PYMODULE'),
  ('reportlab.pdfbase.pdfform',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\pdfbase\\pdfform.py',
   'PYMODULE'),
  ('reportlab.pdfbase.pdfmetrics',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\pdfbase\\pdfmetrics.py',
   'PYMODULE'),
  ('reportlab.pdfbase.pdfpattern',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\pdfbase\\pdfpattern.py',
   'PYMODULE'),
  ('reportlab.pdfbase.pdfutils',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\pdfbase\\pdfutils.py',
   'PYMODULE'),
  ('reportlab.pdfbase.rl_codecs',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\pdfbase\\rl_codecs.py',
   'PYMODULE'),
  ('reportlab.pdfbase.ttfonts',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\pdfbase\\ttfonts.py',
   'PYMODULE'),
  ('reportlab.pdfgen',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\pdfgen\\__init__.py',
   'PYMODULE'),
  ('reportlab.pdfgen.canvas',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\pdfgen\\canvas.py',
   'PYMODULE'),
  ('reportlab.pdfgen.pathobject',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\pdfgen\\pathobject.py',
   'PYMODULE'),
  ('reportlab.pdfgen.pdfgeom',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\pdfgen\\pdfgeom.py',
   'PYMODULE'),
  ('reportlab.pdfgen.pdfimages',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\pdfgen\\pdfimages.py',
   'PYMODULE'),
  ('reportlab.pdfgen.textobject',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\pdfgen\\textobject.py',
   'PYMODULE'),
  ('reportlab.platypus',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\platypus\\__init__.py',
   'PYMODULE'),
  ('reportlab.platypus.doctemplate',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\platypus\\doctemplate.py',
   'PYMODULE'),
  ('reportlab.platypus.figures',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\platypus\\figures.py',
   'PYMODULE'),
  ('reportlab.platypus.flowables',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\platypus\\flowables.py',
   'PYMODULE'),
  ('reportlab.platypus.frames',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\platypus\\frames.py',
   'PYMODULE'),
  ('reportlab.platypus.multicol',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\platypus\\multicol.py',
   'PYMODULE'),
  ('reportlab.platypus.para',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\platypus\\para.py',
   'PYMODULE'),
  ('reportlab.platypus.paragraph',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\platypus\\paragraph.py',
   'PYMODULE'),
  ('reportlab.platypus.paraparser',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\platypus\\paraparser.py',
   'PYMODULE'),
  ('reportlab.platypus.tableofcontents',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\platypus\\tableofcontents.py',
   'PYMODULE'),
  ('reportlab.platypus.tables',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\platypus\\tables.py',
   'PYMODULE'),
  ('reportlab.platypus.xpreformatted',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\platypus\\xpreformatted.py',
   'PYMODULE'),
  ('reportlab.rl_config',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\rl_config.py',
   'PYMODULE'),
  ('reportlab.rl_settings',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\reportlab\\rl_settings.py',
   'PYMODULE'),
  ('rlcompleter',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\rlcompleter.py',
   'PYMODULE'),
  ('runpy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\runpy.py',
   'PYMODULE'),
  ('scanner_utils',
   'C:\\Users\\<USER>\\OneDrive\\Desktop\\test 111\\safaa\\scanner_utils.py',
   'PYMODULE'),
  ('secrets',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\secrets.py',
   'PYMODULE'),
  ('selectors',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\selectors.py',
   'PYMODULE'),
  ('setuptools',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools._core_metadata',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_core_metadata.py',
   'PYMODULE'),
  ('setuptools._discovery',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_discovery.py',
   'PYMODULE'),
  ('setuptools._distutils',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils._log',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\_log.py',
   'PYMODULE'),
  ('setuptools._distutils._modified',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\_modified.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools._distutils.compat',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.numpy',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\compat\\numpy.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.py39',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C.base',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\base.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.errors',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.msvc',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\msvc.py',
   'PYMODULE'),
  ('setuptools._distutils.core',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE'),
  ('setuptools._distutils.errors',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE'),
  ('setuptools._imp',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools._importlib',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE'),
  ('setuptools._itertools',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE'),
  ('setuptools._normalization',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_normalization.py',
   'PYMODULE'),
  ('setuptools._path',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_path.py',
   'PYMODULE'),
  ('setuptools._reqs',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE'),
  ('setuptools._shutil',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_shutil.py',
   'PYMODULE'),
  ('setuptools._static',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_static.py',
   'PYMODULE'),
  ('setuptools._vendor', '-', 'PYMODULE'),
  ('setuptools._vendor.backports',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat.py38',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\py38.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py311',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py39',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco', '-', 'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\functools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._elffile',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._parser',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._tokenizer',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.convert',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\convert.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.pack',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\pack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.tags',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.unpack',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\unpack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.macosx_libfile',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\macosx_libfile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.metadata',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\metadata.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.util',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\util.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._elffile',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._manylinux',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._musllinux',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._parser',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._structures',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._tokenizer',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.markers',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.requirements',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.specifiers',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.tags',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.utils',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.version',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.wheelfile',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\wheelfile.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat.py310',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.glob',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\glob.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools.command',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools.command._requirestxt',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\command\\_requirestxt.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.command.bdist_wheel',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\command\\bdist_wheel.py',
   'PYMODULE'),
  ('setuptools.command.build',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\command\\build.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.compat',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py310',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools.compat.py311',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools.compat.py39',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools.config',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE'),
  ('setuptools.depends',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools.discovery',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\discovery.py',
   'PYMODULE'),
  ('setuptools.dist',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.errors',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\errors.py',
   'PYMODULE'),
  ('setuptools.extension',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools.glob',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.installer',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.logging',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.version',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools.warnings',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\warnings.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('shlex',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\shlex.py',
   'PYMODULE'),
  ('shutil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\shutil.py',
   'PYMODULE'),
  ('signal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\signal.py',
   'PYMODULE'),
  ('site',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site.py',
   'PYMODULE'),
  ('socket',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\socket.py',
   'PYMODULE'),
  ('socketserver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\socketserver.py',
   'PYMODULE'),
  ('sqlalchemy',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.connectors',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\connectors\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.connectors.aioodbc',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\connectors\\aioodbc.py',
   'PYMODULE'),
  ('sqlalchemy.connectors.asyncio',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\connectors\\asyncio.py',
   'PYMODULE'),
  ('sqlalchemy.connectors.pyodbc',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\connectors\\pyodbc.py',
   'PYMODULE'),
  ('sqlalchemy.cyextension',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\cyextension\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects._typing',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\_typing.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mssql\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.aioodbc',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mssql\\aioodbc.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.base',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mssql\\base.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.information_schema',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mssql\\information_schema.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.json',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mssql\\json.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.pymssql',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mssql\\pymssql.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.pyodbc',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mssql\\pyodbc.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.aiomysql',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\aiomysql.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.asyncmy',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\asyncmy.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.base',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\base.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.cymysql',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\cymysql.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.dml',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\dml.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.enumerated',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\enumerated.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.expression',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\expression.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.json',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\json.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.mariadb',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\mariadb.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.mariadbconnector',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\mariadbconnector.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.mysqlconnector',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\mysqlconnector.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.mysqldb',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\mysqldb.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.pymysql',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\pymysql.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.pyodbc',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\pyodbc.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.reflection',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\reflection.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.reserved_words',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\reserved_words.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.types',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\types.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\oracle\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle.base',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\oracle\\base.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle.cx_oracle',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\oracle\\cx_oracle.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle.dictionary',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\oracle\\dictionary.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle.oracledb',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\oracle\\oracledb.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle.types',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\oracle\\types.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle.vector',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\oracle\\vector.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql._psycopg_common',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\_psycopg_common.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.array',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\array.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.asyncpg',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\asyncpg.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.base',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\base.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.dml',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\dml.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.ext',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\ext.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.hstore',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\hstore.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.json',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\json.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.named_types',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\named_types.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.operators',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\operators.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.pg8000',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\pg8000.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.pg_catalog',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\pg_catalog.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.psycopg',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\psycopg.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.psycopg2',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\psycopg2.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.psycopg2cffi',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\psycopg2cffi.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.ranges',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\ranges.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.types',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\types.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.aiosqlite',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\aiosqlite.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.base',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\base.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.dml',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\dml.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.json',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\json.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.pysqlcipher',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\pysqlcipher.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.pysqlite',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\pysqlite.py',
   'PYMODULE'),
  ('sqlalchemy.engine',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.engine._py_processors',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\_py_processors.py',
   'PYMODULE'),
  ('sqlalchemy.engine._py_row',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\_py_row.py',
   'PYMODULE'),
  ('sqlalchemy.engine._py_util',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\_py_util.py',
   'PYMODULE'),
  ('sqlalchemy.engine.base',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\base.py',
   'PYMODULE'),
  ('sqlalchemy.engine.characteristics',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\characteristics.py',
   'PYMODULE'),
  ('sqlalchemy.engine.create',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\create.py',
   'PYMODULE'),
  ('sqlalchemy.engine.cursor',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\cursor.py',
   'PYMODULE'),
  ('sqlalchemy.engine.default',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\default.py',
   'PYMODULE'),
  ('sqlalchemy.engine.events',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\events.py',
   'PYMODULE'),
  ('sqlalchemy.engine.interfaces',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\interfaces.py',
   'PYMODULE'),
  ('sqlalchemy.engine.mock',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\mock.py',
   'PYMODULE'),
  ('sqlalchemy.engine.processors',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\processors.py',
   'PYMODULE'),
  ('sqlalchemy.engine.reflection',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\reflection.py',
   'PYMODULE'),
  ('sqlalchemy.engine.result',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\result.py',
   'PYMODULE'),
  ('sqlalchemy.engine.row',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\row.py',
   'PYMODULE'),
  ('sqlalchemy.engine.url',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\url.py',
   'PYMODULE'),
  ('sqlalchemy.engine.util',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\util.py',
   'PYMODULE'),
  ('sqlalchemy.event',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\event\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.event.api',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\event\\api.py',
   'PYMODULE'),
  ('sqlalchemy.event.attr',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\event\\attr.py',
   'PYMODULE'),
  ('sqlalchemy.event.base',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\event\\base.py',
   'PYMODULE'),
  ('sqlalchemy.event.legacy',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\event\\legacy.py',
   'PYMODULE'),
  ('sqlalchemy.event.registry',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\event\\registry.py',
   'PYMODULE'),
  ('sqlalchemy.exc',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\exc.py',
   'PYMODULE'),
  ('sqlalchemy.ext',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\ext\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\ext\\asyncio\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio.base',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\ext\\asyncio\\base.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio.engine',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\ext\\asyncio\\engine.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio.exc',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\ext\\asyncio\\exc.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio.result',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\ext\\asyncio\\result.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio.scoping',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\ext\\asyncio\\scoping.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio.session',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\ext\\asyncio\\session.py',
   'PYMODULE'),
  ('sqlalchemy.ext.baked',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\ext\\baked.py',
   'PYMODULE'),
  ('sqlalchemy.ext.compiler',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\ext\\compiler.py',
   'PYMODULE'),
  ('sqlalchemy.future',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\future\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.future.engine',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\future\\engine.py',
   'PYMODULE'),
  ('sqlalchemy.inspection',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\inspection.py',
   'PYMODULE'),
  ('sqlalchemy.log',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\log.py',
   'PYMODULE'),
  ('sqlalchemy.orm',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.orm._orm_constructors',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\_orm_constructors.py',
   'PYMODULE'),
  ('sqlalchemy.orm._typing',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\_typing.py',
   'PYMODULE'),
  ('sqlalchemy.orm.attributes',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\attributes.py',
   'PYMODULE'),
  ('sqlalchemy.orm.base',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\base.py',
   'PYMODULE'),
  ('sqlalchemy.orm.bulk_persistence',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\bulk_persistence.py',
   'PYMODULE'),
  ('sqlalchemy.orm.clsregistry',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\clsregistry.py',
   'PYMODULE'),
  ('sqlalchemy.orm.collections',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\collections.py',
   'PYMODULE'),
  ('sqlalchemy.orm.context',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\context.py',
   'PYMODULE'),
  ('sqlalchemy.orm.decl_api',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\decl_api.py',
   'PYMODULE'),
  ('sqlalchemy.orm.decl_base',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\decl_base.py',
   'PYMODULE'),
  ('sqlalchemy.orm.dependency',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\dependency.py',
   'PYMODULE'),
  ('sqlalchemy.orm.descriptor_props',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\descriptor_props.py',
   'PYMODULE'),
  ('sqlalchemy.orm.dynamic',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\dynamic.py',
   'PYMODULE'),
  ('sqlalchemy.orm.evaluator',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\evaluator.py',
   'PYMODULE'),
  ('sqlalchemy.orm.events',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\events.py',
   'PYMODULE'),
  ('sqlalchemy.orm.exc',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\exc.py',
   'PYMODULE'),
  ('sqlalchemy.orm.identity',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\identity.py',
   'PYMODULE'),
  ('sqlalchemy.orm.instrumentation',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\instrumentation.py',
   'PYMODULE'),
  ('sqlalchemy.orm.interfaces',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\interfaces.py',
   'PYMODULE'),
  ('sqlalchemy.orm.loading',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\loading.py',
   'PYMODULE'),
  ('sqlalchemy.orm.mapped_collection',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\mapped_collection.py',
   'PYMODULE'),
  ('sqlalchemy.orm.mapper',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\mapper.py',
   'PYMODULE'),
  ('sqlalchemy.orm.path_registry',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\path_registry.py',
   'PYMODULE'),
  ('sqlalchemy.orm.persistence',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\persistence.py',
   'PYMODULE'),
  ('sqlalchemy.orm.properties',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\properties.py',
   'PYMODULE'),
  ('sqlalchemy.orm.query',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\query.py',
   'PYMODULE'),
  ('sqlalchemy.orm.relationships',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\relationships.py',
   'PYMODULE'),
  ('sqlalchemy.orm.scoping',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\scoping.py',
   'PYMODULE'),
  ('sqlalchemy.orm.session',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\session.py',
   'PYMODULE'),
  ('sqlalchemy.orm.state',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\state.py',
   'PYMODULE'),
  ('sqlalchemy.orm.state_changes',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\state_changes.py',
   'PYMODULE'),
  ('sqlalchemy.orm.strategies',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\strategies.py',
   'PYMODULE'),
  ('sqlalchemy.orm.strategy_options',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\strategy_options.py',
   'PYMODULE'),
  ('sqlalchemy.orm.sync',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\sync.py',
   'PYMODULE'),
  ('sqlalchemy.orm.unitofwork',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\unitofwork.py',
   'PYMODULE'),
  ('sqlalchemy.orm.util',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\util.py',
   'PYMODULE'),
  ('sqlalchemy.orm.writeonly',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\writeonly.py',
   'PYMODULE'),
  ('sqlalchemy.pool',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\pool\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.pool.base',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\pool\\base.py',
   'PYMODULE'),
  ('sqlalchemy.pool.events',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\pool\\events.py',
   'PYMODULE'),
  ('sqlalchemy.pool.impl',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\pool\\impl.py',
   'PYMODULE'),
  ('sqlalchemy.schema',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\schema.py',
   'PYMODULE'),
  ('sqlalchemy.sql',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.sql._dml_constructors',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\_dml_constructors.py',
   'PYMODULE'),
  ('sqlalchemy.sql._elements_constructors',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\_elements_constructors.py',
   'PYMODULE'),
  ('sqlalchemy.sql._orm_types',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\_orm_types.py',
   'PYMODULE'),
  ('sqlalchemy.sql._py_util',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\_py_util.py',
   'PYMODULE'),
  ('sqlalchemy.sql._selectable_constructors',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\_selectable_constructors.py',
   'PYMODULE'),
  ('sqlalchemy.sql._typing',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\_typing.py',
   'PYMODULE'),
  ('sqlalchemy.sql.annotation',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\annotation.py',
   'PYMODULE'),
  ('sqlalchemy.sql.base',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\base.py',
   'PYMODULE'),
  ('sqlalchemy.sql.cache_key',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\cache_key.py',
   'PYMODULE'),
  ('sqlalchemy.sql.coercions',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\coercions.py',
   'PYMODULE'),
  ('sqlalchemy.sql.compiler',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\compiler.py',
   'PYMODULE'),
  ('sqlalchemy.sql.crud',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\crud.py',
   'PYMODULE'),
  ('sqlalchemy.sql.ddl',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\ddl.py',
   'PYMODULE'),
  ('sqlalchemy.sql.default_comparator',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\default_comparator.py',
   'PYMODULE'),
  ('sqlalchemy.sql.dml',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\dml.py',
   'PYMODULE'),
  ('sqlalchemy.sql.elements',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\elements.py',
   'PYMODULE'),
  ('sqlalchemy.sql.events',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\events.py',
   'PYMODULE'),
  ('sqlalchemy.sql.expression',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\expression.py',
   'PYMODULE'),
  ('sqlalchemy.sql.functions',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\functions.py',
   'PYMODULE'),
  ('sqlalchemy.sql.lambdas',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\lambdas.py',
   'PYMODULE'),
  ('sqlalchemy.sql.naming',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\naming.py',
   'PYMODULE'),
  ('sqlalchemy.sql.operators',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\operators.py',
   'PYMODULE'),
  ('sqlalchemy.sql.roles',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\roles.py',
   'PYMODULE'),
  ('sqlalchemy.sql.schema',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\schema.py',
   'PYMODULE'),
  ('sqlalchemy.sql.selectable',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\selectable.py',
   'PYMODULE'),
  ('sqlalchemy.sql.sqltypes',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\sqltypes.py',
   'PYMODULE'),
  ('sqlalchemy.sql.traversals',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\traversals.py',
   'PYMODULE'),
  ('sqlalchemy.sql.type_api',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\type_api.py',
   'PYMODULE'),
  ('sqlalchemy.sql.util',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\util.py',
   'PYMODULE'),
  ('sqlalchemy.sql.visitors',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\visitors.py',
   'PYMODULE'),
  ('sqlalchemy.types',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\types.py',
   'PYMODULE'),
  ('sqlalchemy.util',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\util\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.util._collections',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\util\\_collections.py',
   'PYMODULE'),
  ('sqlalchemy.util._concurrency_py3k',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\util\\_concurrency_py3k.py',
   'PYMODULE'),
  ('sqlalchemy.util._has_cy',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\util\\_has_cy.py',
   'PYMODULE'),
  ('sqlalchemy.util._py_collections',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\util\\_py_collections.py',
   'PYMODULE'),
  ('sqlalchemy.util.compat',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\util\\compat.py',
   'PYMODULE'),
  ('sqlalchemy.util.concurrency',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\util\\concurrency.py',
   'PYMODULE'),
  ('sqlalchemy.util.deprecations',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\util\\deprecations.py',
   'PYMODULE'),
  ('sqlalchemy.util.langhelpers',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\util\\langhelpers.py',
   'PYMODULE'),
  ('sqlalchemy.util.preloaded',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\util\\preloaded.py',
   'PYMODULE'),
  ('sqlalchemy.util.queue',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\util\\queue.py',
   'PYMODULE'),
  ('sqlalchemy.util.topological',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\util\\topological.py',
   'PYMODULE'),
  ('sqlalchemy.util.typing',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\sqlalchemy\\util\\typing.py',
   'PYMODULE'),
  ('sqlite3',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\sqlite3\\__init__.py',
   'PYMODULE'),
  ('sqlite3.__main__',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\sqlite3\\__main__.py',
   'PYMODULE'),
  ('sqlite3.dbapi2',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\sqlite3\\dbapi2.py',
   'PYMODULE'),
  ('sqlite3.dump',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\sqlite3\\dump.py',
   'PYMODULE'),
  ('ssl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ssl.py',
   'PYMODULE'),
  ('statistics',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\statistics.py',
   'PYMODULE'),
  ('string',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\string.py',
   'PYMODULE'),
  ('stringprep',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\stringprep.py',
   'PYMODULE'),
  ('subprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\subprocess.py',
   'PYMODULE'),
  ('sysconfig',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\sysconfig\\__init__.py',
   'PYMODULE'),
  ('tarfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\tarfile.py',
   'PYMODULE'),
  ('tempfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\tempfile.py',
   'PYMODULE'),
  ('textwrap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\textwrap.py',
   'PYMODULE'),
  ('threading',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\threading.py',
   'PYMODULE'),
  ('token',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\token.py',
   'PYMODULE'),
  ('tokenize',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\tokenize.py',
   'PYMODULE'),
  ('tomllib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\tomllib\\__init__.py',
   'PYMODULE'),
  ('tomllib._parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\tomllib\\_parser.py',
   'PYMODULE'),
  ('tomllib._re',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\tomllib\\_re.py',
   'PYMODULE'),
  ('tomllib._types',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\tomllib\\_types.py',
   'PYMODULE'),
  ('tracemalloc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\tracemalloc.py',
   'PYMODULE'),
  ('tty',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\tty.py',
   'PYMODULE'),
  ('typing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\typing.py',
   'PYMODULE'),
  ('typing_extensions',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('unittest',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest._log',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\_log.py',
   'PYMODULE'),
  ('unittest.async_case',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.case',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest.loader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.main',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.mock',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\mock.py',
   'PYMODULE'),
  ('unittest.result',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.runner',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.signals',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.suite',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\util.py',
   'PYMODULE'),
  ('urllib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('urllib.error',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\urllib\\error.py',
   'PYMODULE'),
  ('urllib.parse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib.request',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\urllib\\request.py',
   'PYMODULE'),
  ('urllib.response',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\urllib\\response.py',
   'PYMODULE'),
  ('uuid',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\uuid.py',
   'PYMODULE'),
  ('webbrowser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\webbrowser.py',
   'PYMODULE'),
  ('werkzeug',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\werkzeug\\__init__.py',
   'PYMODULE'),
  ('werkzeug._internal',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\werkzeug\\_internal.py',
   'PYMODULE'),
  ('werkzeug._reloader',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\werkzeug\\_reloader.py',
   'PYMODULE'),
  ('werkzeug.datastructures',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\werkzeug\\datastructures\\__init__.py',
   'PYMODULE'),
  ('werkzeug.datastructures.accept',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\werkzeug\\datastructures\\accept.py',
   'PYMODULE'),
  ('werkzeug.datastructures.auth',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\werkzeug\\datastructures\\auth.py',
   'PYMODULE'),
  ('werkzeug.datastructures.cache_control',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\werkzeug\\datastructures\\cache_control.py',
   'PYMODULE'),
  ('werkzeug.datastructures.csp',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\werkzeug\\datastructures\\csp.py',
   'PYMODULE'),
  ('werkzeug.datastructures.etag',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\werkzeug\\datastructures\\etag.py',
   'PYMODULE'),
  ('werkzeug.datastructures.file_storage',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\werkzeug\\datastructures\\file_storage.py',
   'PYMODULE'),
  ('werkzeug.datastructures.headers',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\werkzeug\\datastructures\\headers.py',
   'PYMODULE'),
  ('werkzeug.datastructures.mixins',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\werkzeug\\datastructures\\mixins.py',
   'PYMODULE'),
  ('werkzeug.datastructures.range',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\werkzeug\\datastructures\\range.py',
   'PYMODULE'),
  ('werkzeug.datastructures.structures',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\werkzeug\\datastructures\\structures.py',
   'PYMODULE'),
  ('werkzeug.debug',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\werkzeug\\debug\\__init__.py',
   'PYMODULE'),
  ('werkzeug.debug.console',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\werkzeug\\debug\\console.py',
   'PYMODULE'),
  ('werkzeug.debug.repr',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\werkzeug\\debug\\repr.py',
   'PYMODULE'),
  ('werkzeug.debug.tbtools',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\werkzeug\\debug\\tbtools.py',
   'PYMODULE'),
  ('werkzeug.exceptions',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\werkzeug\\exceptions.py',
   'PYMODULE'),
  ('werkzeug.formparser',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\werkzeug\\formparser.py',
   'PYMODULE'),
  ('werkzeug.http',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\werkzeug\\http.py',
   'PYMODULE'),
  ('werkzeug.local',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\werkzeug\\local.py',
   'PYMODULE'),
  ('werkzeug.middleware',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\werkzeug\\middleware\\__init__.py',
   'PYMODULE'),
  ('werkzeug.middleware.shared_data',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\werkzeug\\middleware\\shared_data.py',
   'PYMODULE'),
  ('werkzeug.routing',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\werkzeug\\routing\\__init__.py',
   'PYMODULE'),
  ('werkzeug.routing.converters',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\werkzeug\\routing\\converters.py',
   'PYMODULE'),
  ('werkzeug.routing.exceptions',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\werkzeug\\routing\\exceptions.py',
   'PYMODULE'),
  ('werkzeug.routing.map',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\werkzeug\\routing\\map.py',
   'PYMODULE'),
  ('werkzeug.routing.matcher',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\werkzeug\\routing\\matcher.py',
   'PYMODULE'),
  ('werkzeug.routing.rules',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\werkzeug\\routing\\rules.py',
   'PYMODULE'),
  ('werkzeug.sansio',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\werkzeug\\sansio\\__init__.py',
   'PYMODULE'),
  ('werkzeug.sansio.http',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\werkzeug\\sansio\\http.py',
   'PYMODULE'),
  ('werkzeug.sansio.multipart',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\werkzeug\\sansio\\multipart.py',
   'PYMODULE'),
  ('werkzeug.sansio.request',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\werkzeug\\sansio\\request.py',
   'PYMODULE'),
  ('werkzeug.sansio.response',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\werkzeug\\sansio\\response.py',
   'PYMODULE'),
  ('werkzeug.sansio.utils',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\werkzeug\\sansio\\utils.py',
   'PYMODULE'),
  ('werkzeug.security',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\werkzeug\\security.py',
   'PYMODULE'),
  ('werkzeug.serving',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\werkzeug\\serving.py',
   'PYMODULE'),
  ('werkzeug.test',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\werkzeug\\test.py',
   'PYMODULE'),
  ('werkzeug.urls',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\werkzeug\\urls.py',
   'PYMODULE'),
  ('werkzeug.user_agent',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\werkzeug\\user_agent.py',
   'PYMODULE'),
  ('werkzeug.utils',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\werkzeug\\utils.py',
   'PYMODULE'),
  ('werkzeug.wrappers',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\werkzeug\\wrappers\\__init__.py',
   'PYMODULE'),
  ('werkzeug.wrappers.request',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\werkzeug\\wrappers\\request.py',
   'PYMODULE'),
  ('werkzeug.wrappers.response',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\werkzeug\\wrappers\\response.py',
   'PYMODULE'),
  ('werkzeug.wsgi',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\werkzeug\\wsgi.py',
   'PYMODULE'),
  ('wtforms',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\wtforms\\__init__.py',
   'PYMODULE'),
  ('wtforms.csrf',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\wtforms\\csrf\\__init__.py',
   'PYMODULE'),
  ('wtforms.csrf.core',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\wtforms\\csrf\\core.py',
   'PYMODULE'),
  ('wtforms.csrf.session',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\wtforms\\csrf\\session.py',
   'PYMODULE'),
  ('wtforms.fields',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\wtforms\\fields\\__init__.py',
   'PYMODULE'),
  ('wtforms.fields.choices',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\wtforms\\fields\\choices.py',
   'PYMODULE'),
  ('wtforms.fields.core',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\wtforms\\fields\\core.py',
   'PYMODULE'),
  ('wtforms.fields.datetime',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\wtforms\\fields\\datetime.py',
   'PYMODULE'),
  ('wtforms.fields.form',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\wtforms\\fields\\form.py',
   'PYMODULE'),
  ('wtforms.fields.list',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\wtforms\\fields\\list.py',
   'PYMODULE'),
  ('wtforms.fields.numeric',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\wtforms\\fields\\numeric.py',
   'PYMODULE'),
  ('wtforms.fields.simple',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\wtforms\\fields\\simple.py',
   'PYMODULE'),
  ('wtforms.form',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\wtforms\\form.py',
   'PYMODULE'),
  ('wtforms.i18n',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\wtforms\\i18n.py',
   'PYMODULE'),
  ('wtforms.meta',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\wtforms\\meta.py',
   'PYMODULE'),
  ('wtforms.utils',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\wtforms\\utils.py',
   'PYMODULE'),
  ('wtforms.validators',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\wtforms\\validators.py',
   'PYMODULE'),
  ('wtforms.widgets',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\wtforms\\widgets\\__init__.py',
   'PYMODULE'),
  ('wtforms.widgets.core',
   'c:\\Users\\<USER>\\OneDrive\\Desktop\\test '
   '111\\safaa\\.venv\\Lib\\site-packages\\wtforms\\widgets\\core.py',
   'PYMODULE'),
  ('xml',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\__init__.py',
   'PYMODULE'),
  ('xml.dom',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\dom\\__init__.py',
   'PYMODULE'),
  ('xml.dom.NodeFilter',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\dom\\NodeFilter.py',
   'PYMODULE'),
  ('xml.dom.domreg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\dom\\domreg.py',
   'PYMODULE'),
  ('xml.dom.expatbuilder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\dom\\expatbuilder.py',
   'PYMODULE'),
  ('xml.dom.minicompat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\dom\\minicompat.py',
   'PYMODULE'),
  ('xml.dom.minidom',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\dom\\minidom.py',
   'PYMODULE'),
  ('xml.dom.pulldom',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\dom\\pulldom.py',
   'PYMODULE'),
  ('xml.dom.xmlbuilder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\dom\\xmlbuilder.py',
   'PYMODULE'),
  ('xml.etree',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\etree\\__init__.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.parsers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.sax',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('xmlrpc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('zipfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\zipfile\\__init__.py',
   'PYMODULE'),
  ('zipfile._path',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE'),
  ('zipimport',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\zipimport.py',
   'PYMODULE')])
