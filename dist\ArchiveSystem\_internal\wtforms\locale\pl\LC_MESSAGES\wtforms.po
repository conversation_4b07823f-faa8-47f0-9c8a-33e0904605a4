# Polish translations for WTForms.
# Copyright (C) 2020 WTForms Team
# This file is distributed under the same license as the WTForms project.
# <AUTHOR> <EMAIL>, 2020.
#
msgid ""
msgstr ""
"Project-Id-Version: WTForms 1.0\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2024-01-11 08:20+0100\n"
"PO-Revision-Date: 2012-05-05 23:20+0200\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: pl <<EMAIL>>\n"
"Language: pl\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=(n==1 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 "
"|| n%100>=20) ? 1 : 2)\n"
"Generated-By: Babel 2.8.0\n"

#: src/wtforms/validators.py:86
#, python-format
msgid "Invalid field name '%s'."
msgstr "Nieprawidłowa nazwa pola '%s'."

#: src/wtforms/validators.py:99
#, python-format
msgid "Field must be equal to %(other_name)s."
msgstr "Wartość pola musi być równa %(other_name)s."

#: src/wtforms/validators.py:145
#, python-format
msgid "Field must be at least %(min)d character long."
msgid_plural "Field must be at least %(min)d characters long."
msgstr[0] "Pole musi mieć przynajmniej %(min)d znak."
msgstr[1] "Pole musi mieć przynajmniej %(min)d znaki."
msgstr[2] "Pole musi mieć przynajmniej %(min)d znaków."

#: src/wtforms/validators.py:151
#, python-format
msgid "Field cannot be longer than %(max)d character."
msgid_plural "Field cannot be longer than %(max)d characters."
msgstr[0] "Wartość w polu nie może mieć więcej niż %(max)d znak."
msgstr[1] "Wartość w polu nie może mieć więcej niż %(max)d znaki."
msgstr[2] "Wartość w polu nie może mieć więcej niż %(max)d znaków."

#: src/wtforms/validators.py:157
#, python-format
msgid "Field must be exactly %(max)d character long."
msgid_plural "Field must be exactly %(max)d characters long."
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""

#: src/wtforms/validators.py:163
#, python-format
msgid "Field must be between %(min)d and %(max)d characters long."
msgstr "Wartość musi być długa na od %(min)d do %(max)d znaków."

#: src/wtforms/validators.py:216
#, python-format
msgid "Number must be at least %(min)s."
msgstr "Liczba musi być większa lub równa %(min)s."

#: src/wtforms/validators.py:219
#, python-format
msgid "Number must be at most %(max)s."
msgstr "Liczba musi być mniejsza lub równa %(max)s."

#: src/wtforms/validators.py:222
#, python-format
msgid "Number must be between %(min)s and %(max)s."
msgstr "Liczba musi być z zakresu %(min)s i %(max)s."

#: src/wtforms/validators.py:293 src/wtforms/validators.py:323
msgid "This field is required."
msgstr "To pole jest wymagane."

#: src/wtforms/validators.py:358
msgid "Invalid input."
msgstr "Nieprawidłowa wartość."

#: src/wtforms/validators.py:422
msgid "Invalid email address."
msgstr "Nieprawidłowy adres e-mail."

#: src/wtforms/validators.py:460
msgid "Invalid IP address."
msgstr "Nieprawidłowy adres IP."

#: src/wtforms/validators.py:503
msgid "Invalid Mac address."
msgstr "Nieprawidłowy adres Mac."

#: src/wtforms/validators.py:540
msgid "Invalid URL."
msgstr "Nieprawidłowy URL."

#: src/wtforms/validators.py:561
msgid "Invalid UUID."
msgstr "Nieprawidłowy UUID."

#: src/wtforms/validators.py:594
#, python-format
msgid "Invalid value, must be one of: %(values)s."
msgstr "Wartość musi być jedną z: %(values)s."

#: src/wtforms/validators.py:629
#, python-format
msgid "Invalid value, can't be any of: %(values)s."
msgstr "Wartość nie może być żadną z: %(values)s."

#: src/wtforms/validators.py:698
#, fuzzy
#| msgid "This field is required."
msgid "This field cannot be edited."
msgstr "To pole jest wymagane."

#: src/wtforms/validators.py:714
msgid "This field is disabled and cannot have a value."
msgstr ""

#: src/wtforms/csrf/core.py:96
msgid "Invalid CSRF Token."
msgstr "Nieprawidłowy token CSRF"

#: src/wtforms/csrf/session.py:63
msgid "CSRF token missing."
msgstr "Brak tokena CSRF"

#: src/wtforms/csrf/session.py:71
msgid "CSRF failed."
msgstr "błąd CSRF"

#: src/wtforms/csrf/session.py:76
msgid "CSRF token expired."
msgstr "Wygasł token CSRF"

#: src/wtforms/fields/choices.py:142
msgid "Invalid Choice: could not coerce."
msgstr "Nieprawidłowy wybór: nie można skonwertować"

#: src/wtforms/fields/choices.py:149 src/wtforms/fields/choices.py:203
msgid "Choices cannot be None."
msgstr ""

#: src/wtforms/fields/choices.py:155
msgid "Not a valid choice."
msgstr "Nieprawidłowy wybór"

#: src/wtforms/fields/choices.py:193
msgid "Invalid choice(s): one or more data inputs could not be coerced."
msgstr ""
"Nieprawidłowy wybór: nie można skonwertować przynajmniej jednej wartości"

#: src/wtforms/fields/choices.py:214
#, fuzzy, python-format
#| msgid "'%(value)s' is not a valid choice for this field."
msgid "'%(value)s' is not a valid choice for this field."
msgid_plural "'%(value)s' are not valid choices for this field."
msgstr[0] "'%(value)s' nie jest poprawnym wyborem dla tego pola"
msgstr[1] "'%(value)s' nie jest poprawnym wyborem dla tego pola"
msgstr[2] "'%(value)s' nie jest poprawnym wyborem dla tego pola"

#: src/wtforms/fields/datetime.py:51
msgid "Not a valid datetime value."
msgstr "Nieprawidłowa data i czas"

#: src/wtforms/fields/datetime.py:77
msgid "Not a valid date value."
msgstr "Nieprawidłowa data"

#: src/wtforms/fields/datetime.py:103
msgid "Not a valid time value."
msgstr ""

#: src/wtforms/fields/datetime.py:148
#, fuzzy
#| msgid "Not a valid date value."
msgid "Not a valid week value."
msgstr "Nieprawidłowa data"

#: src/wtforms/fields/numeric.py:82 src/wtforms/fields/numeric.py:92
msgid "Not a valid integer value."
msgstr "Nieprawidłowa liczba całkowita"

#: src/wtforms/fields/numeric.py:168
msgid "Not a valid decimal value."
msgstr "Nieprawidłowa liczba dziesiętna"

#: src/wtforms/fields/numeric.py:197
msgid "Not a valid float value."
msgstr "Nieprawidłowa liczba zmiennoprzecinkowa"
