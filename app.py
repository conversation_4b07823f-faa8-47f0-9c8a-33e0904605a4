from flask import Flask, render_template, request, redirect, url_for, flash, jsonify, send_file
from flask_login import Login<PERSON>anager, login_user, login_required, logout_user, current_user
from werkzeug.utils import secure_filename
from datetime import datetime
import os

from functools import wraps
from models import db, User, Document, DocumentAttachment, DocumentMovement, Department, DocumentType, AuditLog
from scanner_utils import scanner_manager
from markupsafe import Markup
import re

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-here-change-in-production'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///archive.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# إنشاء مجلد الرفع إذا لم يكن موجوداً
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

db.init_app(app)
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'
login_manager.login_message = 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة.'
login_manager.login_message_category = 'info'

# إضافة فلتر nl2br المخصص
@app.template_filter('nl2br')
def nl2br_filter(text):
    """تحويل أسطر جديدة إلى <br> tags"""
    if text is None:
        return ''
    # تحويل \n إلى <br>
    result = re.sub(r'\r?\n', '<br>', str(text))
    return Markup(result)

# إضافة CSRF token إلى السياق العام للقوالب
@app.context_processor
def inject_csrf_token():
    from flask_wtf.csrf import generate_csrf
    return dict(csrf_token=generate_csrf())

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# دالة للتحقق من الصلاحيات
def permission_required(permission):
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not current_user.is_authenticated:
                return redirect(url_for('login'))
            if not current_user.has_permission(permission):
                flash('ليس لديك صلاحية للوصول إلى هذه الصفحة.', 'error')
                return redirect(url_for('dashboard'))
            return f(*args, **kwargs)
        return decorated_function
    return decorator

# المسارات الأساسية
@app.route('/')
def index():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))
    return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))

    from forms import LoginForm
    form = LoginForm()

    if form.validate_on_submit():
        user = User.query.filter_by(username=form.username.data).first()
        if user and user.check_password(form.password.data) and user.is_active:
            login_user(user)
            user.last_login = datetime.now()
            db.session.commit()

            # تسجيل عملية الدخول
            AuditLog.log_action(
                user_id=user.id,
                action='login',
                ip_address=request.remote_addr,
                user_agent=request.headers.get('User-Agent')
            )

            next_page = request.args.get('next')
            if not next_page or not next_page.startswith('/'):
                next_page = url_for('dashboard')
            return redirect(next_page)
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة.', 'error')

    return render_template('auth/login.html', form=form)

@app.route('/logout')
@login_required
def logout():
    # تسجيل عملية الخروج
    AuditLog.log_action(
        user_id=current_user.id,
        action='logout',
        ip_address=request.remote_addr,
        user_agent=request.headers.get('User-Agent')
    )
    logout_user()
    flash('تم تسجيل الخروج بنجاح.', 'success')
    return redirect(url_for('login'))

@app.route('/dashboard')
@login_required
def dashboard():
    # إحصائيات سريعة
    total_documents = Document.query.count()
    incoming_documents = Document.query.filter_by(document_type='incoming').count()
    outgoing_documents = Document.query.filter_by(document_type='outgoing').count()
    internal_documents = Document.query.filter_by(document_type='internal').count()

    # الكتب الحديثة
    recent_documents = Document.query.order_by(Document.created_at.desc()).limit(10).all()

    # الكتب المحولة إلي
    my_documents = DocumentMovement.query.filter_by(
        to_user_id=current_user.id,
        is_read=False
    ).order_by(DocumentMovement.created_at.desc()).limit(5).all()

    return render_template('dashboard.html',
                         total_documents=total_documents,
                         incoming_documents=incoming_documents,
                         outgoing_documents=outgoing_documents,
                         internal_documents=internal_documents,
                         recent_documents=recent_documents,
                         my_documents=my_documents)

# مسارات إدارة الكتب
@app.route('/documents')
@login_required
def documents():
    page = request.args.get('page', 1, type=int)
    per_page = 20

    documents = Document.query.order_by(Document.created_at.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )

    return render_template('documents/list.html', documents=documents)

@app.route('/documents/add', methods=['GET', 'POST'])
@login_required
@permission_required('create')
def add_document():
    from forms import DocumentForm
    form = DocumentForm()

    if form.validate_on_submit():
        # إنشاء الكتاب الجديد
        document = Document(
            document_number=form.document_number.data,
            document_date=form.document_date.data,
            subject=form.subject.data,
            content=form.content.data,
            document_type=form.document_type.data,
            document_type_id=form.document_type_id.data if form.document_type_id.data != 0 else None,
            department_id=form.department_id.data if form.department_id.data != 0 else None,
            sender=form.sender.data,
            receiver=form.receiver.data,
            classification=form.classification.data,
            priority=form.priority.data,
            created_by=current_user.id
        )

        db.session.add(document)
        db.session.flush()  # للحصول على ID الكتاب

        # رفع المرفقات العادية
        attachments_count = 0
        if form.attachments.data:
            file = form.attachments.data
            if file and file.filename:
                filename = secure_filename(file.filename)
                # إنشاء اسم ملف فريد
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                filename = f"{timestamp}_{filename}"
                file_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
                file.save(file_path)

                # حفظ معلومات المرفق
                attachment = DocumentAttachment(
                    document_id=document.id,
                    filename=filename,
                    original_filename=file.filename,
                    file_size=os.path.getsize(file_path),
                    file_type=file.content_type,
                    file_path=file_path,
                    uploaded_by=current_user.id
                )
                db.session.add(attachment)
                attachments_count += 1

        # معالجة الصور الممسوحة
        scanned_images = request.form.getlist('scanned_images')
        if scanned_images:
            for i in range(len(request.form.getlist('scanned_images[0][filename]'))):
                try:
                    scanned_filename = request.form.get(f'scanned_images[{i}][filename]')
                    display_name = request.form.get(f'scanned_images[{i}][display_name]')

                    if scanned_filename:
                        # التحقق من وجود الملف
                        scanned_file_path = os.path.join(app.config['UPLOAD_FOLDER'], scanned_filename)
                        if os.path.exists(scanned_file_path):
                            # حفظ معلومات الصورة الممسوحة
                            attachment = DocumentAttachment(
                                document_id=document.id,
                                filename=scanned_filename,
                                original_filename=display_name or scanned_filename,
                                file_size=os.path.getsize(scanned_file_path),
                                file_type='image/jpeg',
                                file_path=scanned_file_path,
                                uploaded_by=current_user.id
                            )
                            db.session.add(attachment)
                            attachments_count += 1
                except Exception as e:
                    print(f"خطأ في معالجة الصورة الممسوحة: {e}")

        # تحديث معلومات المرفقات
        if attachments_count > 0:
            document.has_attachments = True
            document.attachments_count = attachments_count

        db.session.commit()

        # تسجيل العملية
        AuditLog.log_action(
            user_id=current_user.id,
            action='create',
            table_name='document',
            record_id=document.id,
            new_values={'document_number': document.document_number, 'subject': document.subject},
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent')
        )

        flash('تم إضافة الكتاب بنجاح.', 'success')
        return redirect(url_for('view_document', id=document.id))

    return render_template('documents/add.html', form=form)

@app.route('/documents/<int:id>')
@login_required
def view_document(id):
    document = Document.query.get_or_404(id)
    movements = DocumentMovement.query.filter_by(document_id=id).order_by(DocumentMovement.created_at.desc()).all()

    return render_template('documents/view.html', document=document, movements=movements)

@app.route('/documents/<int:id>/edit', methods=['GET', 'POST'])
@login_required
@permission_required('update')
def edit_document(id):
    document = Document.query.get_or_404(id)
    from forms import DocumentForm
    form = DocumentForm(obj=document)

    if form.validate_on_submit():
        old_values = {
            'document_number': document.document_number,
            'subject': document.subject,
            'content': document.content
        }

        form.populate_obj(document)
        document.updated_at = datetime.now()

        # رفع مرفق جديد إذا تم اختياره
        if form.attachments.data:
            file = form.attachments.data
            if file and file.filename:
                filename = secure_filename(file.filename)
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                filename = f"{timestamp}_{filename}"
                file_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
                file.save(file_path)

                attachment = DocumentAttachment(
                    document_id=document.id,
                    filename=filename,
                    original_filename=file.filename,
                    file_size=os.path.getsize(file_path),
                    file_type=file.content_type,
                    file_path=file_path,
                    uploaded_by=current_user.id
                )
                db.session.add(attachment)

                document.has_attachments = True
                document.attachments_count = document.attachments.count() + 1

        db.session.commit()

        # تسجيل العملية
        AuditLog.log_action(
            user_id=current_user.id,
            action='update',
            table_name='document',
            record_id=document.id,
            old_values=old_values,
            new_values={'document_number': document.document_number, 'subject': document.subject},
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent')
        )

        flash('تم تحديث الكتاب بنجاح.', 'success')
        return redirect(url_for('view_document', id=document.id))

    return render_template('documents/edit.html', form=form, document=document)

# مسار البحث المتقدم
@app.route('/search', methods=['GET', 'POST'])
@login_required
def search():
    from forms import SearchForm
    form = SearchForm()
    results = None

    if form.validate_on_submit() or request.method == 'GET':
        query = Document.query

        # البحث النصي
        if form.search_text.data:
            search_term = f"%{form.search_text.data}%"
            query = query.filter(
                db.or_(
                    Document.subject.like(search_term),
                    Document.content.like(search_term),
                    Document.document_number.like(search_term)
                )
            )

        # رقم الكتاب
        if form.document_number.data:
            query = query.filter(Document.document_number.like(f"%{form.document_number.data}%"))

        # التاريخ من
        if form.date_from.data:
            query = query.filter(Document.document_date >= form.date_from.data)

        # التاريخ إلى
        if form.date_to.data:
            query = query.filter(Document.document_date <= form.date_to.data)

        # نوع الكتاب
        if form.document_type.data:
            query = query.filter(Document.document_type == form.document_type.data)

        # القسم
        if form.department_id.data and form.department_id.data != 0:
            query = query.filter(Document.department_id == form.department_id.data)

        # درجة السرية
        if form.classification.data:
            query = query.filter(Document.classification == form.classification.data)

        # الأولوية
        if form.priority.data:
            query = query.filter(Document.priority == form.priority.data)

        # الجهة المرسلة
        if form.sender.data:
            query = query.filter(Document.sender.like(f"%{form.sender.data}%"))

        # الجهة المستقبلة
        if form.receiver.data:
            query = query.filter(Document.receiver.like(f"%{form.receiver.data}%"))

        # تنفيذ البحث مع التصفح
        page = request.args.get('page', 1, type=int)
        results = query.order_by(Document.created_at.desc()).paginate(
            page=page, per_page=20, error_out=False
        )

    return render_template('search.html', form=form, results=results)

# مسار تحميل المرفقات
@app.route('/attachments/<int:id>/download')
@login_required
def download_attachment(id):
    attachment = DocumentAttachment.query.get_or_404(id)

    # التحقق من الصلاحيات
    if not current_user.has_permission('read'):
        flash('ليس لديك صلاحية لتحميل هذا المرفق.', 'error')
        return redirect(url_for('dashboard'))

    return send_file(attachment.file_path, as_attachment=True, download_name=attachment.original_filename)

# API endpoints
@app.route('/api/users')
@login_required
def api_users():
    users = User.query.filter_by(is_active=True).all()
    return jsonify([{
        'id': user.id,
        'full_name': user.full_name,
        'department': user.department
    } for user in users])

@app.route('/api/transfer-document', methods=['POST'])
@login_required
@permission_required('create')
def api_transfer_document():
    try:
        document_id = request.form.get('document_id')
        to_user_id = request.form.get('to_user_id')
        movement_type = request.form.get('movement_type')
        notes = request.form.get('notes')

        if not all([document_id, to_user_id, movement_type]):
            return jsonify({'success': False, 'message': 'بيانات غير مكتملة'}), 400

        document = Document.query.get_or_404(document_id)
        to_user = User.query.get_or_404(to_user_id)

        # إنشاء حركة جديدة
        movement = DocumentMovement(
            document_id=document.id,
            from_user=current_user.full_name,
            to_user=to_user.full_name,
            to_user_id=to_user.id,
            movement_type=movement_type,
            notes=notes,
            created_by=current_user.id
        )

        db.session.add(movement)
        db.session.commit()

        # تسجيل العملية
        AuditLog.log_action(
            user_id=current_user.id,
            action='transfer',
            table_name='document',
            record_id=document.id,
            new_values={'to_user': to_user.full_name, 'movement_type': movement_type},
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent')
        )

        return jsonify({'success': True, 'message': 'تم تحويل الكتاب بنجاح'})

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

# مسارات إدارة المستخدمين
@app.route('/users')
@login_required
@permission_required('manage_users')
def users():
    page = request.args.get('page', 1, type=int)
    users = User.query.paginate(page=page, per_page=20, error_out=False)
    return render_template('users/list.html', users=users)

@app.route('/users/add', methods=['GET', 'POST'])
@login_required
@permission_required('manage_users')
def add_user():
    from forms import UserForm
    form = UserForm()

    if form.validate_on_submit():
        user = User(
            username=form.username.data,
            email=f"{form.username.data}@example.com",  # إيميل افتراضي
            full_name=form.full_name.data,
            role=form.role.data,
            department=form.department.data,
            is_active=form.is_active.data
        )
        if form.password.data:
            user.set_password(form.password.data)
        else:
            user.set_password('123456')  # كلمة مرور افتراضية

        db.session.add(user)
        db.session.commit()

        flash('تم إضافة المستخدم بنجاح.', 'success')
        return redirect(url_for('users'))

    return render_template('users/add.html', form=form)

@app.route('/users/<int:id>/edit', methods=['GET', 'POST'])
@login_required
@permission_required('manage_users')
def edit_user(id):
    user = User.query.get_or_404(id)
    from forms import UserForm
    form = UserForm(obj=user, user=user)

    if form.validate_on_submit():
        user.username = form.username.data
        user.full_name = form.full_name.data
        user.role = form.role.data
        user.department = form.department.data
        user.is_active = form.is_active.data

        if form.password.data:
            user.set_password(form.password.data)

        db.session.commit()
        flash('تم تحديث المستخدم بنجاح.', 'success')
        return redirect(url_for('users'))

    return render_template('users/edit.html', form=form, user=user)

@app.route('/api/users/<int:id>')
@login_required
@permission_required('manage_users')
def api_get_user(id):
    """جلب بيانات مستخدم محدد"""
    user = User.query.get_or_404(id)
    return jsonify({
        'id': user.id,
        'username': user.username,
        'full_name': user.full_name,
        'email': user.email,
        'role': user.role,
        'department': user.department,
        'is_active': user.is_active,
        'last_login': user.last_login.isoformat() if user.last_login else None
    })

@app.route('/api/users/<int:id>/toggle-status', methods=['POST'])
@login_required
@permission_required('manage_users')
def api_toggle_user_status(id):
    """تبديل حالة تفعيل المستخدم"""
    try:
        user = User.query.get_or_404(id)

        # منع إلغاء تفعيل المستخدم الحالي
        if user.id == current_user.id:
            return jsonify({'success': False, 'message': 'لا يمكنك إلغاء تفعيل حسابك الخاص'}), 400

        user.is_active = not user.is_active
        db.session.commit()

        status = 'تم تفعيل' if user.is_active else 'تم إلغاء تفعيل'
        return jsonify({'success': True, 'message': f'{status} المستخدم بنجاح'})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/users/<int:id>', methods=['DELETE'])
@login_required
@permission_required('manage_users')
def api_delete_user(id):
    """حذف مستخدم"""
    try:
        user = User.query.get_or_404(id)

        # منع حذف المستخدم الحالي
        if user.id == current_user.id:
            return jsonify({'success': False, 'message': 'لا يمكنك حذف حسابك الخاص'}), 400

        # التحقق من عدم وجود كتب مرتبطة بهذا المستخدم
        if user.created_documents.count() > 0 or user.received_movements.count() > 0:
            return jsonify({'success': False, 'message': 'لا يمكن حذف المستخدم لوجود كتب أو حركات مرتبطة به'}), 400

        db.session.delete(user)
        db.session.commit()
        return jsonify({'success': True, 'message': 'تم حذف المستخدم بنجاح'})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

# مسارات التقارير
@app.route('/reports')
@login_required
@permission_required('view_reports')
def reports():
    from forms import ReportForm
    form = ReportForm()
    return render_template('reports/index.html', form=form)

@app.route('/reports/generate', methods=['POST'])
@login_required
@permission_required('view_reports')
def generate_report():
    print("تم استلام طلب إنشاء تقرير")
    print("البيانات المرسلة:", request.form)

    # استخراج البيانات مباشرة من request.form بدلاً من النموذج
    report_type = request.form.get('report_type')
    date_from_str = request.form.get('date_from')
    date_to_str = request.form.get('date_to')

    print(f"نوع التقرير: {report_type}")
    print(f"من تاريخ: {date_from_str}")
    print(f"إلى تاريخ: {date_to_str}")

    if report_type and date_from_str and date_to_str:
        try:
            from datetime import datetime
            date_from = datetime.strptime(date_from_str, '%Y-%m-%d').date()
            date_to = datetime.strptime(date_to_str, '%Y-%m-%d').date()
            print("تم تحويل التواريخ بنجاح")
        except ValueError as e:
            print(f"خطأ في تنسيق التاريخ: {e}")
            return jsonify({"error": "تنسيق التاريخ غير صحيح"}), 400

        # بناء الاستعلام الأساسي
        query = Document.query.filter(
            Document.document_date >= date_from,
            Document.document_date <= date_to
        )

        # تطبيق المرشحات الإضافية
        department_id = request.form.get('department_id')
        if department_id and int(department_id) != 0:
            query = query.filter(Document.department_id == int(department_id))

        document_type = request.form.get('document_type')
        if document_type:
            query = query.filter(Document.document_type == document_type)

        documents = query.all()

        # إنشاء البيانات حسب نوع التقرير
        report_data = {}

        if report_type == 'documents_summary':
            report_data = {
                'total': len(documents),
                'incoming': len([d for d in documents if d.document_type == 'incoming']),
                'outgoing': len([d for d in documents if d.document_type == 'outgoing']),
                'internal': len([d for d in documents if d.document_type == 'internal']),
                'urgent': len([d for d in documents if d.priority == 'urgent']),
                'high': len([d for d in documents if d.priority == 'high']),
                'normal': len([d for d in documents if d.priority == 'normal']),
                'low': len([d for d in documents if d.priority == 'low'])
            }

        elif report_type == 'documents_by_type':
            from collections import Counter
            types = Counter([d.document_type for d in documents])
            report_data = dict(types)

        elif report_type == 'documents_by_department':
            from collections import Counter
            departments = Counter([d.department_ref.name if d.department_ref else 'غير محدد' for d in documents])
            report_data = dict(departments)

        elif report_type == 'user_activity':
            from collections import Counter
            creators = Counter([d.creator.full_name for d in documents])
            report_data = dict(creators)

        from datetime import datetime
        return render_template('reports/result.html',
                             report_type=report_type,
                             report_data=report_data,
                             documents=documents,
                             date_from=date_from,
                             date_to=date_to,
                             moment=datetime.now)
    else:
        print("البيانات غير صحيحة، إعادة عرض الصفحة")
        from forms import ReportForm
        form = ReportForm()
        return render_template('reports/index.html', form=form)

@app.route('/reports/export/excel', methods=['POST'])
@login_required
@permission_required('view_reports')
def export_report_excel():
    """تصدير التقرير إلى ملف Excel"""
    print("تم استلام طلب تصدير Excel")
    print("البيانات المرسلة:", request.form)
    import openpyxl
    from openpyxl.styles import Font, PatternFill, Alignment
    from openpyxl.utils import get_column_letter
    from datetime import datetime
    import io

    # استخراج البيانات مباشرة من request.form بدلاً من النموذج
    report_type = request.form.get('report_type')
    date_from_str = request.form.get('date_from')
    date_to_str = request.form.get('date_to')

    print(f"نوع التقرير: {report_type}")
    print(f"من تاريخ: {date_from_str}")
    print(f"إلى تاريخ: {date_to_str}")

    if report_type and date_from_str and date_to_str:
        try:
            from datetime import datetime
            date_from = datetime.strptime(date_from_str, '%Y-%m-%d').date()
            date_to = datetime.strptime(date_to_str, '%Y-%m-%d').date()
        except ValueError:
            print("خطأ في تنسيق التاريخ")
            return jsonify({"error": "تنسيق التاريخ غير صحيح"}), 400

        # بناء الاستعلام الأساسي
        query = Document.query.filter(
            Document.document_date >= date_from,
            Document.document_date <= date_to
        )

        # تطبيق المرشحات الإضافية
        department_id = request.form.get('department_id')
        if department_id and int(department_id) != 0:
            query = query.filter(Document.department_id == int(department_id))

        document_type = request.form.get('document_type')
        if document_type:
            query = query.filter(Document.document_type == document_type)

        documents = query.all()

        # إنشاء ملف Excel
        wb = openpyxl.Workbook()
        ws = wb.active

        # تحديد عنوان التقرير
        report_titles = {
            'documents_summary': 'ملخص الكتب',
            'documents_by_type': 'الكتب حسب النوع',
            'documents_by_department': 'الكتب حسب القسم',
            'user_activity': 'نشاط المستخدمين'
        }

        ws.title = report_titles.get(report_type, 'تقرير')

        # إعداد الأنماط
        header_font = Font(bold=True, color="FFFFFF")
        header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
        center_alignment = Alignment(horizontal="center", vertical="center")

        # إضافة معلومات التقرير
        ws['A1'] = f"تقرير: {report_titles.get(report_type, 'تقرير')}"
        ws['A1'].font = Font(bold=True, size=16)
        ws['A2'] = f"من تاريخ: {date_from.strftime('%Y-%m-%d')}"
        ws['A3'] = f"إلى تاريخ: {date_to.strftime('%Y-%m-%d')}"
        ws['A4'] = f"تاريخ الإنشاء: {datetime.now().strftime('%Y-%m-%d %H:%M')}"

        # إضافة البيانات حسب نوع التقرير
        if report_type == 'documents_summary':
            # إحصائيات عامة
            ws['A6'] = "الإحصائيات العامة"
            ws['A6'].font = header_font
            ws['A6'].fill = header_fill

            ws['A7'] = "إجمالي الكتب"
            ws['B7'] = len(documents)

            ws['A8'] = "كتب واردة"
            ws['B8'] = len([d for d in documents if d.document_type == 'incoming'])

            ws['A9'] = "كتب صادرة"
            ws['B9'] = len([d for d in documents if d.document_type == 'outgoing'])

            ws['A10'] = "كتب داخلية"
            ws['B10'] = len([d for d in documents if d.document_type == 'internal'])

            # تفاصيل الكتب
            ws['A12'] = "تفاصيل الكتب"
            ws['A12'].font = header_font
            ws['A12'].fill = header_fill

            headers = ['رقم الكتاب', 'الموضوع', 'النوع', 'الأولوية', 'التاريخ', 'القسم']
            for col, header in enumerate(headers, 1):
                cell = ws.cell(row=13, column=col, value=header)
                cell.font = header_font
                cell.fill = header_fill
                cell.alignment = center_alignment

            for row, doc in enumerate(documents, 14):
                ws.cell(row=row, column=1, value=doc.document_number)
                ws.cell(row=row, column=2, value=doc.subject)
                ws.cell(row=row, column=3, value={'incoming': 'وارد', 'outgoing': 'صادر', 'internal': 'داخلي'}.get(doc.document_type, doc.document_type))
                ws.cell(row=row, column=4, value={'urgent': 'عاجل', 'high': 'عالي', 'normal': 'عادي', 'low': 'منخفض'}.get(doc.priority, doc.priority))
                ws.cell(row=row, column=5, value=doc.document_date.strftime('%Y-%m-%d') if doc.document_date else '')
                ws.cell(row=row, column=6, value=doc.department_ref.name if doc.department_ref else 'غير محدد')

        else:
            # التقارير الأخرى
            from collections import Counter

            if report_type == 'documents_by_type':
                data = Counter([d.document_type for d in documents])
                title = "توزيع الكتب حسب النوع"
            elif report_type == 'documents_by_department':
                data = Counter([d.department_ref.name if d.department_ref else 'غير محدد' for d in documents])
                title = "توزيع الكتب حسب القسم"
            elif report_type == 'user_activity':
                data = Counter([d.creator.full_name for d in documents])
                title = "نشاط المستخدمين"

            ws['A6'] = title
            ws['A6'].font = header_font
            ws['A6'].fill = header_fill

            # رؤوس الأعمدة
            ws['A7'] = "الفئة"
            ws['B7'] = "العدد"
            ws['C7'] = "النسبة"

            for col in range(1, 4):
                cell = ws.cell(row=7, column=col)
                cell.font = header_font
                cell.fill = header_fill
                cell.alignment = center_alignment

            # البيانات
            total = sum(data.values())
            for row, (key, value) in enumerate(data.items(), 8):
                ws.cell(row=row, column=1, value=key)
                ws.cell(row=row, column=2, value=value)
                ws.cell(row=row, column=3, value=f"{(value / total * 100):.1f}%" if total > 0 else "0%")

        # تنسيق الأعمدة
        for column in ws.columns:
            max_length = 0
            column_letter = get_column_letter(column[0].column)
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)
            ws.column_dimensions[column_letter].width = adjusted_width

        # حفظ الملف في الذاكرة
        output = io.BytesIO()
        wb.save(output)
        output.seek(0)

        # إنشاء اسم الملف
        filename = f"report_{report_type}_{date_from.strftime('%Y%m%d')}_{date_to.strftime('%Y%m%d')}.xlsx"

        return send_file(
            output,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name=filename
        )

    return redirect(url_for('reports'))

@app.route('/reports/export/pdf', methods=['POST'])
@login_required
@permission_required('view_reports')
def export_report_pdf():
    """تصدير التقرير إلى ملف PDF"""
    print("تم استلام طلب تصدير PDF")
    print("البيانات المرسلة:", request.form)

    from reportlab.lib.pagesizes import A4, landscape
    from reportlab.lib import colors
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch
    from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
    from reportlab.pdfbase import pdfmetrics
    from reportlab.pdfbase.ttfonts import TTFont
    from datetime import datetime
    import io
    import os

    # استخراج البيانات مباشرة من request.form
    report_type = request.form.get('report_type')
    date_from_str = request.form.get('date_from')
    date_to_str = request.form.get('date_to')

    print(f"نوع التقرير PDF: {report_type}")
    print(f"من تاريخ PDF: {date_from_str}")
    print(f"إلى تاريخ PDF: {date_to_str}")

    if report_type and date_from_str and date_to_str:
        try:
            date_from = datetime.strptime(date_from_str, '%Y-%m-%d').date()
            date_to = datetime.strptime(date_to_str, '%Y-%m-%d').date()
        except ValueError:
            print("خطأ في تنسيق التاريخ PDF")
            return jsonify({"error": "تنسيق التاريخ غير صحيح"}), 400

        # بناء الاستعلام الأساسي
        query = Document.query.filter(
            Document.document_date >= date_from,
            Document.document_date <= date_to
        )

        # تطبيق المرشحات الإضافية
        department_id = request.form.get('department_id')
        if department_id and int(department_id) != 0:
            query = query.filter(Document.department_id == int(department_id))

        document_type = request.form.get('document_type')
        if document_type:
            query = query.filter(Document.document_type == document_type)

        documents = query.all()

        # إنشاء ملف PDF
        output = io.BytesIO()

        # تحديد عنوان التقرير - نسخة إنجليزية للـ PDF
        report_titles = {
            'documents_summary': 'Documents Summary Report',
            'documents_by_type': 'Documents by Type Report',
            'documents_by_department': 'Documents by Department Report',
            'user_activity': 'User Activity Report'
        }

        # عناوين عربية للعرض
        report_titles_ar = {
            'documents_summary': 'ملخص الكتب',
            'documents_by_type': 'الكتب حسب النوع',
            'documents_by_department': 'الكتب حسب القسم',
            'user_activity': 'نشاط المستخدمين'
        }

        # إنشاء المستند
        if report_type == 'documents_summary' and len(documents) > 10:
            pdf_doc = SimpleDocTemplate(output, pagesize=landscape(A4))
        else:
            pdf_doc = SimpleDocTemplate(output, pagesize=A4)

        # إعداد الخطوط العربية
        try:
            # محاولة تسجيل خط عربي من النظام
            arabic_font_paths = [
                'C:/Windows/Fonts/arial.ttf',  # Arial Unicode MS
                'C:/Windows/Fonts/tahoma.ttf',  # Tahoma
                'C:/Windows/Fonts/calibri.ttf',  # Calibri
            ]

            arabic_font_name = 'Helvetica'  # افتراضي
            for font_path in arabic_font_paths:
                if os.path.exists(font_path):
                    try:
                        pdfmetrics.registerFont(TTFont('Arabic', font_path))
                        arabic_font_name = 'Arabic'
                        print(f"تم تسجيل الخط العربي: {font_path}")
                        break
                    except Exception as e:
                        print(f"فشل في تسجيل الخط {font_path}: {e}")
                        continue
        except Exception as e:
            print(f"خطأ في إعداد الخطوط: {e}")
            arabic_font_name = 'Helvetica'

        # إعداد الأنماط
        styles = getSampleStyleSheet()

        title_style = ParagraphStyle(
            'ArabicTitle',
            parent=styles['Title'],
            fontSize=16,
            alignment=1,  # Center alignment
            fontName=arabic_font_name
        )

        normal_style = ParagraphStyle(
            'ArabicNormal',
            parent=styles['Normal'],
            fontSize=12,
            fontName=arabic_font_name,
            alignment=2  # Right alignment for Arabic
        )

        # محتوى المستند
        story = []

        # العنوان - استخدام ترميز أفضل للعربية
        title_text = f"تقرير: {report_titles.get(report_type, 'تقرير')}"
        try:
            # محاولة ترميز النص بشكل صحيح
            title_text = title_text.encode('utf-8').decode('utf-8')
        except:
            pass

        title = Paragraph(title_text, title_style)
        story.append(title)
        story.append(Spacer(1, 12))

        # معلومات التقرير
        info_data = [
            ['From Date:', date_from.strftime('%Y-%m-%d')],
            ['To Date:', date_to.strftime('%Y-%m-%d')],
            ['Generated:', datetime.now().strftime('%Y-%m-%d %H:%M')]
        ]

        info_table = Table(info_data, colWidths=[2*inch, 3*inch])
        info_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, -1), colors.lightgrey),
            ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
            ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
            ('FONTNAME', (0, 0), (-1, -1), arabic_font_name),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))

        story.append(info_table)
        story.append(Spacer(1, 20))

        # إضافة البيانات حسب نوع التقرير
        if report_type == 'documents_summary':
            # إحصائيات عامة
            stats_title = Paragraph("General Statistics", title_style)
            story.append(stats_title)
            story.append(Spacer(1, 12))

            stats_data = [
                ['Total Documents', str(len(documents))],
                ['Incoming Documents', str(len([d for d in documents if d.document_type == 'incoming']))],
                ['Outgoing Documents', str(len([d for d in documents if d.document_type == 'outgoing']))],
                ['Internal Documents', str(len([d for d in documents if d.document_type == 'internal']))]
            ]

            stats_table = Table(stats_data, colWidths=[3*inch, 2*inch])
            stats_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
                ('FONTNAME', (0, 0), (-1, -1), arabic_font_name),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))

            story.append(stats_table)
            story.append(Spacer(1, 20))

            # تفاصيل الكتب (أول 50 كتاب فقط لتجنب ملفات PDF كبيرة جداً)
            if documents:
                details_title = Paragraph("Document Details", title_style)
                story.append(details_title)
                story.append(Spacer(1, 12))

                headers = ['Doc Number', 'Subject', 'Type', 'Date']
                table_data = [headers]

                for document in documents[:50]:  # أول 50 كتاب فقط
                    row = [
                        document.document_number or '',
                        document.subject[:30] + '...' if len(document.subject) > 30 else document.subject,
                        {'incoming': 'Incoming', 'outgoing': 'Outgoing', 'internal': 'Internal'}.get(document.document_type, document.document_type),
                        document.document_date.strftime('%Y-%m-%d') if document.document_date else ''
                    ]
                    table_data.append(row)

                if len(documents) > 50:
                    table_data.append(['...', f'and {len(documents) - 50} more documents', '', ''])

                details_table = Table(table_data, colWidths=[1.5*inch, 3*inch, 1*inch, 1*inch])
                details_table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
                    ('FONTNAME', (0, 0), (-1, -1), arabic_font_name),
                    ('FONTSIZE', (0, 0), (-1, -1), 8),
                    ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black),
                    ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.lightgrey])
                ]))

                story.append(details_table)

        else:
            # التقارير الأخرى
            from collections import Counter

            if report_type == 'documents_by_type':
                data = Counter([d.document_type for d in documents])
                chart_title = "Documents Distribution by Type"
            elif report_type == 'documents_by_department':
                data = Counter([d.department_ref.name if d.department_ref else 'Unspecified' for d in documents])
                chart_title = "Documents Distribution by Department"
            elif report_type == 'user_activity':
                data = Counter([d.creator.full_name for d in documents])
                chart_title = "User Activity Report"

            chart_title_para = Paragraph(chart_title, title_style)
            story.append(chart_title_para)
            story.append(Spacer(1, 12))

            # جدول البيانات
            headers = ['Category', 'Count', 'Percentage']
            table_data = [headers]

            total = sum(data.values())
            for key, value in data.items():
                percentage = f"{(value / total * 100):.1f}%" if total > 0 else "0%"
                table_data.append([key, str(value), percentage])

            data_table = Table(table_data, colWidths=[3*inch, 1*inch, 1*inch])
            data_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
                ('FONTNAME', (0, 0), (-1, -1), arabic_font_name),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.lightgrey])
            ]))

            story.append(data_table)

        # بناء المستند
        pdf_doc.build(story)
        output.seek(0)

        # إنشاء اسم الملف
        filename = f"report_{report_type}_{date_from.strftime('%Y%m%d')}_{date_to.strftime('%Y%m%d')}.pdf"

        return send_file(
            output,
            mimetype='application/pdf',
            as_attachment=True,
            download_name=filename
        )

    return redirect(url_for('reports'))

@app.route('/reports/export/simple-excel', methods=['POST', 'GET'])
def simple_export_excel():
    """تصدير مبسط لاختبار المشكلة"""
    print("تم استلام طلب تصدير Excel مبسط")
    print("الطريقة:", request.method)
    print("البيانات:", request.form if request.method == 'POST' else 'GET request')

    try:
        import openpyxl
        import io
        from datetime import datetime

        # إنشاء ملف Excel بسيط
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "تقرير"

        # إضافة بيانات بسيطة
        ws['A1'] = "تقرير الكتب"
        ws['A2'] = f"تاريخ الإنشاء: {datetime.now().strftime('%Y-%m-%d %H:%M')}"
        ws['A3'] = "رقم الكتاب"
        ws['B3'] = "الموضوع"

        # إضافة بعض البيانات من قاعدة البيانات
        documents = Document.query.limit(10).all()
        for i, doc in enumerate(documents, 4):
            ws[f'A{i}'] = doc.document_number
            ws[f'B{i}'] = doc.subject

        # حفظ في الذاكرة
        output = io.BytesIO()
        wb.save(output)
        output.seek(0)

        print("تم إنشاء الملف بنجاح")

        return send_file(
            output,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name='simple_report.xlsx'
        )

    except Exception as e:
        print(f"خطأ في التصدير: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/test-export')
def test_export():
    """اختبار بسيط للتصدير"""
    return jsonify({"message": "اختبار التصدير يعمل", "status": "success"})

# مسارات الإعدادات
@app.route('/settings')
@login_required
@permission_required('manage_users')
def settings():
    departments = Department.query.all()
    document_types = DocumentType.query.all()
    return render_template('settings/index.html', departments=departments, document_types=document_types)

# API endpoints إضافية
@app.route('/api/documents/delete', methods=['POST'])
@login_required
@permission_required('delete')
def api_delete_documents():
    """حذف كتاب أو مجموعة كتب"""
    try:
        # محاولة الحصول على البيانات من مصادر مختلفة
        document_ids = request.form.getlist('document_ids')
        if not document_ids:
            # محاولة الحصول على البيانات من JSON
            json_data = request.get_json()
            if json_data and 'document_ids' in json_data:
                document_ids = json_data['document_ids']

        print(f"Received document_ids: {document_ids}")  # للتشخيص

        if not document_ids:
            return jsonify({'success': False, 'message': 'لم يتم تحديد أي كتب للحذف'}), 400

        deleted_count = 0
        errors = []

        for doc_id in document_ids:
            try:
                document = Document.query.get(doc_id)
                if not document:
                    errors.append(f'الكتاب رقم {doc_id} غير موجود')
                    continue

                # التحقق من الصلاحيات - يمكن للمستخدم حذف كتبه فقط أو للمدير حذف أي كتاب
                if not (current_user.role == 'admin' or document.created_by == current_user.id):
                    errors.append(f'ليس لديك صلاحية لحذف الكتاب رقم {document.get_full_number()}')
                    continue

                # حذف المرفقات المرتبطة
                for attachment in document.attachments:
                    try:
                        if os.path.exists(attachment.file_path):
                            os.remove(attachment.file_path)
                    except:
                        pass  # تجاهل أخطاء حذف الملفات
                    db.session.delete(attachment)

                # حذف الحركات المرتبطة
                DocumentMovement.query.filter_by(document_id=document.id).delete()

                # حذف الكتاب
                db.session.delete(document)
                deleted_count += 1

            except Exception as e:
                errors.append(f'خطأ في حذف الكتاب رقم {doc_id}: {str(e)}')

        # حفظ التغييرات
        db.session.commit()

        # إعداد الرسالة
        if deleted_count > 0:
            message = f'تم حذف {deleted_count} كتاب بنجاح'
            if errors:
                message += f' مع وجود {len(errors)} خطأ'
            return jsonify({
                'success': True,
                'message': message,
                'deleted_count': deleted_count,
                'errors': errors
            })
        else:
            return jsonify({
                'success': False,
                'message': 'لم يتم حذف أي كتاب',
                'errors': errors
            }), 400

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': f'حدث خطأ: {str(e)}'}), 500

@app.route('/api/documents/<int:document_id>/preview')
@login_required
def api_document_preview(document_id):
    """جلب صورة معاينة للكتاب"""
    try:
        document = Document.query.get_or_404(document_id)

        # البحث عن أول مرفق صورة
        image_attachment = None
        for attachment in document.attachments:
            if attachment.file_type and attachment.file_type.startswith('image/'):
                image_attachment = attachment
                break

        if image_attachment:
            # إنشاء URL للصورة
            image_url = url_for('serve_attachment_preview', attachment_id=image_attachment.id)
            return jsonify({
                'success': True,
                'image_url': image_url,
                'filename': image_attachment.original_filename,
                'document_number': document.get_full_number(),
                'subject': document.subject
            })
        else:
            return jsonify({
                'success': False,
                'message': 'لا توجد صورة متاحة لهذا الكتاب'
            })

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/attachments/<int:attachment_id>/preview')
@login_required
def serve_attachment_preview(attachment_id):
    """عرض صورة المرفق للمعاينة"""
    try:
        attachment = DocumentAttachment.query.get_or_404(attachment_id)

        # التحقق من أن المرفق صورة
        if not attachment.file_type or not attachment.file_type.startswith('image/'):
            return "ليس ملف صورة", 400

        # التحقق من وجود الملف
        if not os.path.exists(attachment.file_path):
            return "الملف غير موجود", 404

        return send_file(attachment.file_path, mimetype=attachment.file_type)

    except Exception as e:
        return str(e), 500

@app.route('/api/departments', methods=['POST'])
@login_required
@permission_required('manage_users')
def api_add_department():
    try:
        name = request.form.get('name')
        code = request.form.get('code')
        description = request.form.get('description')
        is_active = request.form.get('is_active') == 'true'

        if not name or not code:
            return jsonify({'success': False, 'message': 'الاسم والرمز مطلوبان'}), 400

        # التحقق من عدم وجود قسم بنفس الرمز
        if Department.query.filter_by(code=code).first():
            return jsonify({'success': False, 'message': 'رمز القسم موجود بالفعل'}), 400

        department = Department(
            name=name,
            code=code,
            description=description,
            is_active=is_active
        )

        db.session.add(department)
        db.session.commit()

        return jsonify({'success': True, 'message': 'تم إضافة القسم بنجاح'})

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/departments/<int:id>')
@login_required
@permission_required('manage_users')
def api_get_department(id):
    """جلب بيانات قسم محدد"""
    department = Department.query.get_or_404(id)
    return jsonify({
        'id': department.id,
        'name': department.name,
        'code': department.code,
        'description': department.description,
        'is_active': department.is_active
    })

@app.route('/api/departments/<int:id>', methods=['PUT'])
@login_required
@permission_required('manage_users')
def api_update_department(id):
    """تحديث قسم"""
    try:
        department = Department.query.get_or_404(id)

        name = request.form.get('name')
        code = request.form.get('code')
        description = request.form.get('description')
        is_active = request.form.get('is_active') == 'true'

        if not name or not code:
            return jsonify({'success': False, 'message': 'الاسم والرمز مطلوبان'}), 400

        # التحقق من عدم وجود قسم آخر بنفس الرمز
        existing_dept = Department.query.filter_by(code=code).first()
        if existing_dept and existing_dept.id != id:
            return jsonify({'success': False, 'message': 'رمز القسم موجود بالفعل'}), 400

        department.name = name
        department.code = code
        department.description = description
        department.is_active = is_active

        db.session.commit()
        return jsonify({'success': True, 'message': 'تم تحديث القسم بنجاح'})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/departments/<int:id>', methods=['DELETE'])
@login_required
@permission_required('manage_users')
def api_delete_department(id):
    """حذف قسم"""
    try:
        department = Department.query.get_or_404(id)

        # التحقق من عدم وجود كتب مرتبطة بهذا القسم
        if department.documents.count() > 0:
            return jsonify({'success': False, 'message': 'لا يمكن حذف القسم لوجود كتب مرتبطة به'}), 400

        db.session.delete(department)
        db.session.commit()
        return jsonify({'success': True, 'message': 'تم حذف القسم بنجاح'})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/document-types', methods=['POST'])
@login_required
@permission_required('manage_users')
def api_add_document_type():
    try:
        name = request.form.get('name')
        code = request.form.get('code')
        description = request.form.get('description')
        is_active = request.form.get('is_active') == 'true'

        if not name or not code:
            return jsonify({'success': False, 'message': 'الاسم والرمز مطلوبان'}), 400

        # التحقق من عدم وجود نوع بنفس الرمز
        if DocumentType.query.filter_by(code=code).first():
            return jsonify({'success': False, 'message': 'رمز النوع موجود بالفعل'}), 400

        doc_type = DocumentType(
            name=name,
            code=code,
            description=description,
            is_active=is_active
        )

        db.session.add(doc_type)
        db.session.commit()

        return jsonify({'success': True, 'message': 'تم إضافة نوع الكتاب بنجاح'})

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/document-types/<int:id>')
@login_required
@permission_required('manage_users')
def api_get_document_type(id):
    """جلب بيانات نوع كتاب محدد"""
    doc_type = DocumentType.query.get_or_404(id)
    return jsonify({
        'id': doc_type.id,
        'name': doc_type.name,
        'code': doc_type.code,
        'description': doc_type.description,
        'is_active': doc_type.is_active
    })

@app.route('/api/document-types/<int:id>', methods=['PUT'])
@login_required
@permission_required('manage_users')
def api_update_document_type(id):
    """تحديث نوع كتاب"""
    try:
        doc_type = DocumentType.query.get_or_404(id)

        name = request.form.get('name')
        code = request.form.get('code')
        description = request.form.get('description')
        is_active = request.form.get('is_active') == 'true'

        if not name or not code:
            return jsonify({'success': False, 'message': 'الاسم والرمز مطلوبان'}), 400

        # التحقق من عدم وجود نوع آخر بنفس الرمز
        existing_type = DocumentType.query.filter_by(code=code).first()
        if existing_type and existing_type.id != id:
            return jsonify({'success': False, 'message': 'رمز النوع موجود بالفعل'}), 400

        doc_type.name = name
        doc_type.code = code
        doc_type.description = description
        doc_type.is_active = is_active

        db.session.commit()
        return jsonify({'success': True, 'message': 'تم تحديث نوع الكتاب بنجاح'})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/document-types/<int:id>', methods=['DELETE'])
@login_required
@permission_required('manage_users')
def api_delete_document_type(id):
    """حذف نوع كتاب"""
    try:
        doc_type = DocumentType.query.get_or_404(id)

        # التحقق من عدم وجود كتب مرتبطة بهذا النوع
        if doc_type.documents.count() > 0:
            return jsonify({'success': False, 'message': 'لا يمكن حذف النوع لوجود كتب مرتبطة به'}), 400

        db.session.delete(doc_type)
        db.session.commit()
        return jsonify({'success': True, 'message': 'تم حذف نوع الكتاب بنجاح'})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/backup')
@login_required
@permission_required('manage_users')
def api_backup():
    try:
        import shutil
        from datetime import datetime

        # تحديد مسار قاعدة البيانات الصحيح
        db_path = os.path.join('instance', 'archive.db')

        # التحقق من وجود ملف قاعدة البيانات
        if not os.path.exists(db_path):
            return jsonify({'success': False, 'message': 'ملف قاعدة البيانات غير موجود'}), 404

        # إنشاء نسخة احتياطية من قاعدة البيانات
        backup_filename = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
        backup_path = os.path.join('backups', backup_filename)

        # إنشاء مجلد النسخ الاحتياطية إذا لم يكن موجوداً
        os.makedirs('backups', exist_ok=True)

        # نسخ قاعدة البيانات
        shutil.copy2(db_path, backup_path)

        # التحقق من نجاح النسخ
        if not os.path.exists(backup_path):
            return jsonify({'success': False, 'message': 'فشل في إنشاء النسخة الاحتياطية'}), 500

        # حساب حجم الملف للتسجيل
        file_size = os.path.getsize(backup_path)
        file_size_mb = round(file_size / (1024 * 1024), 2)

        # تسجيل نجاح العملية
        print(f"تم إنشاء نسخة احتياطية بنجاح: {backup_filename} ({file_size_mb} MB)")

        return send_file(backup_path, as_attachment=True, download_name=backup_filename)

    except Exception as e:
        return jsonify({'success': False, 'message': f'خطأ في إنشاء النسخة الاحتياطية: {str(e)}'}), 500

@app.route('/api/backup/list')
@login_required
@permission_required('manage_users')
def api_backup_list():
    """عرض قائمة النسخ الاحتياطية المتاحة"""
    try:
        from datetime import datetime

        backups_dir = 'backups'
        if not os.path.exists(backups_dir):
            return jsonify({'success': True, 'backups': []})

        backups = []
        for filename in os.listdir(backups_dir):
            if filename.endswith('.db') and filename.startswith('backup_'):
                file_path = os.path.join(backups_dir, filename)
                file_stat = os.stat(file_path)

                # استخدام st_mtime بدلاً من st_ctime للتوافق
                creation_time = getattr(file_stat, 'st_birthtime', file_stat.st_mtime)

                backups.append({
                    'filename': filename,
                    'size': round(file_stat.st_size / (1024 * 1024), 2),  # MB
                    'created': datetime.fromtimestamp(creation_time).strftime('%Y-%m-%d %H:%M:%S'),
                    'path': file_path
                })

        # ترتيب حسب تاريخ الإنشاء (الأحدث أولاً)
        backups.sort(key=lambda x: x['created'], reverse=True)

        return jsonify({'success': True, 'backups': backups})

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/backup/restore', methods=['POST'])
@login_required
@permission_required('manage_users')
def api_backup_restore():
    """استعادة نسخة احتياطية"""
    try:
        import shutil
        from datetime import datetime

        backup_filename = request.json.get('filename')
        if not backup_filename:
            return jsonify({'success': False, 'message': 'اسم الملف مطلوب'}), 400

        backup_path = os.path.join('backups', backup_filename)
        if not os.path.exists(backup_path):
            return jsonify({'success': False, 'message': 'ملف النسخة الاحتياطية غير موجود'}), 404

        # إنشاء نسخة احتياطية من قاعدة البيانات الحالية قبل الاستعادة
        current_db_path = os.path.join('instance', 'archive.db')
        if os.path.exists(current_db_path):
            current_backup_filename = f"before_restore_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
            current_backup_path = os.path.join('backups', current_backup_filename)
            shutil.copy2(current_db_path, current_backup_path)

        # استعادة النسخة الاحتياطية
        shutil.copy2(backup_path, current_db_path)

        return jsonify({
            'success': True,
            'message': 'تم استعادة النسخة الاحتياطية بنجاح',
            'current_backup': current_backup_filename if 'current_backup_filename' in locals() else None
        })

    except Exception as e:
        return jsonify({'success': False, 'message': f'خطأ في استعادة النسخة الاحتياطية: {str(e)}'}), 500

# مسارات السكانر
@app.route('/api/scanner/detect')
@login_required
def api_detect_scanners():
    """اكتشاف السكانرات المتاحة"""
    try:
        success = scanner_manager.detect_scanners()
        return jsonify({
            'success': success,
            'scanners': scanner_manager.available_scanners
        })
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/scanner/scan', methods=['POST'])
@login_required
@permission_required('create')
def api_scan_document():
    """مسح مستند"""
    try:
        scanner_id = request.form.get('scanner_id')
        resolution = int(request.form.get('resolution', 300))
        mode = request.form.get('mode', 'color')
        format_type = request.form.get('format', 'JPEG')

        settings = {
            'resolution': resolution,
            'mode': mode,
            'format': format_type
        }

        result = scanner_manager.scan_document(scanner_id, settings)

        if result['success']:
            # حفظ الصورة مؤقتاً
            filename = f"document_{datetime.now().strftime('%Y%m%d_%H%M%S')}.jpg"
            save_result = scanner_manager.save_scanned_image(
                result['image_data'],
                filename,
                app.config['UPLOAD_FOLDER']
            )

            if save_result['success']:
                return jsonify({
                    'success': True,
                    'message': 'تم المسح بنجاح',
                    'filename': save_result['filename'],
                    'size': save_result['size'],
                    'demo': result.get('demo', False)
                })
            else:
                return jsonify({'success': False, 'message': 'خطأ في حفظ الصورة'}), 500
        else:
            return jsonify({'success': False, 'message': 'فشل في المسح'}), 500

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/scanner/preview/<filename>')
@login_required
def api_preview_scanned(filename):
    """معاينة الصورة الممسوحة"""
    try:
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        if os.path.exists(file_path):
            return send_file(file_path)
        else:
            return jsonify({'success': False, 'message': 'الملف غير موجود'}), 404
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500



if __name__ == '__main__':
    # إعداد ترميز UTF-8 للنصوص العربية
    import sys
    import os

    # تعيين ترميز UTF-8 للإخراج
    try:
        if hasattr(sys.stdout, 'reconfigure'):
            sys.stdout.reconfigure(encoding='utf-8')
        if hasattr(sys.stderr, 'reconfigure'):
            sys.stderr.reconfigure(encoding='utf-8')
    except:
        pass

    # تعيين متغيرات البيئة للترميز
    os.environ['PYTHONIOENCODING'] = 'utf-8'

    with app.app_context():
        db.create_all()
        # إنشاء مستخدم افتراضي إذا لم يكن موجوداً
        if not User.query.filter_by(username='admin').first():
            admin_user = User(
                username='admin',
                email='<EMAIL>',
                full_name='مدير النظام',
                role='admin',
                department='إدارة النظام'
            )
            admin_user.set_password('admin123')
            db.session.add(admin_user)
            db.session.commit()
            try:
                print("تم إنشاء المستخدم الافتراضي: admin / admin123")
            except UnicodeEncodeError:
                print("Default user created: admin / admin123")

    try:
        print("بدء تشغيل الخادم على http://127.0.0.1:5000")
    except UnicodeEncodeError:
        print("Starting server on http://127.0.0.1:5000")

    app.run(debug=True, host='127.0.0.1', port=5000)
