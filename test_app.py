from flask import Flask

app = Flask(__name__)

@app.route('/')
def hello():
    return '''
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <title>نظام الأرشفة الإلكترونية</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
        <style>
            body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        </style>
    </head>
    <body>
        <div class="container mt-5">
            <div class="row justify-content-center">
                <div class="col-md-8">
                    <div class="card shadow">
                        <div class="card-header bg-primary text-white text-center">
                            <h1>🗂️ نظام الأرشفة الإلكترونية</h1>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-success">
                                <h4>✅ تم تشغيل النظام بنجاح!</h4>
                                <p>نظام الأرشفة الإلكترونية للكتب الرسمية يعمل الآن.</p>
                            </div>
                            
                            <h3>المميزات المتوفرة:</h3>
                            <ul class="list-group">
                                <li class="list-group-item">📝 إدخال الكتب الرسمية (وارد/صادر/داخلي)</li>
                                <li class="list-group-item">🔍 البحث المتقدم بمعايير متعددة</li>
                                <li class="list-group-item">📊 تتبع حركة الكتب والتحويل الإلكتروني</li>
                                <li class="list-group-item">👥 نظام صلاحيات المستخدمين</li>
                                <li class="list-group-item">📎 رفع وإدارة المرفقات</li>
                                <li class="list-group-item">📈 إصدار التقارير والإحصائيات</li>
                                <li class="list-group-item">🗃️ الأرشفة حسب السنوات والتصنيفات</li>
                            </ul>
                            
                            <div class="mt-4">
                                <h4>المستخدمين الافتراضيين:</h4>
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>اسم المستخدم</th>
                                                <th>كلمة المرور</th>
                                                <th>الدور</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td><code>admin</code></td>
                                                <td><code>admin123</code></td>
                                                <td>مدير النظام</td>
                                            </tr>
                                            <tr>
                                                <td><code>manager</code></td>
                                                <td><code>manager123</code></td>
                                                <td>مدير الأرشيف</td>
                                            </tr>
                                            <tr>
                                                <td><code>user</code></td>
                                                <td><code>user123</code></td>
                                                <td>موظف عادي</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            
                            <div class="alert alert-info mt-4">
                                <h5>📋 خطوات التشغيل الكامل:</h5>
                                <ol>
                                    <li>تأكد من تثبيت Python 3.7+</li>
                                    <li>قم بتثبيت المتطلبات: <code>pip install flask flask-sqlalchemy flask-login flask-wtf</code></li>
                                    <li>قم بتشغيل: <code>python init_data.py</code> لإعداد قاعدة البيانات</li>
                                    <li>قم بتشغيل: <code>python app.py</code> لتشغيل النظام الكامل</li>
                                </ol>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </body>
    </html>
    '''

if __name__ == '__main__':
    print("🚀 بدء تشغيل نظام الأرشفة الإلكترونية...")
    print("📍 الرابط: http://localhost:5000")
    print("⏹️  للإيقاف: اضغط Ctrl+C")
    app.run(debug=True, host='0.0.0.0', port=5000)
