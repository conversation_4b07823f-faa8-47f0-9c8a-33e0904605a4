#!/usr/bin/env python3
"""
اختبار النموذج
"""

from app import app
from forms import ReportForm
from datetime import datetime, date

with app.app_context():
    with app.test_request_context():
        # إنشاء نموذج جديد
        form = ReportForm(csrf_token=False)
    
    print("خيارات نوع التقرير:")
    for value, label in form.report_type.choices:
        print(f"  {value}: {label}")
    
    print("\nخيارات الأقسام:")
    for value, label in form.department_id.choices:
        print(f"  {value}: {label}")
    
    print("\nخيارات نوع الكتاب:")
    for value, label in form.document_type.choices:
        print(f"  {value}: {label}")
    
    # اختبار بيانات النموذج
    test_data = {
        'report_type': 'documents_summary',
        'date_from': date.today(),
        'date_to': date.today(),
        'department_id': 0,
        'document_type': '',
        'csrf_token': 'test'
    }
    
    print(f"\nاختبار البيانات: {test_data}")
    
    # محاولة تعبئة النموذج
    form.report_type.data = test_data['report_type']
    form.date_from.data = test_data['date_from']
    form.date_to.data = test_data['date_to']
    form.department_id.data = test_data['department_id']
    form.document_type.data = test_data['document_type']
    
    print(f"بيانات النموذج بعد التعبئة:")
    print(f"  نوع التقرير: {form.report_type.data}")
    print(f"  من تاريخ: {form.date_from.data}")
    print(f"  إلى تاريخ: {form.date_to.data}")
    print(f"  القسم: {form.department_id.data}")
    print(f"  نوع الكتاب: {form.document_type.data}")

    print("\nاختبار تشغيل الخادم...")
    import requests
    try:
        response = requests.get('http://127.0.0.1:5000')
        print(f"حالة الخادم: {response.status_code}")
    except Exception as e:
        print(f"خطأ في الاتصال بالخادم: {e}")
