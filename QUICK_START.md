# دليل البدء السريع - نظام الأرشفة الإلكترونية

## 🚀 التشغيل السريع

### الطريقة الأولى (الموصى بها):
```bash
python run.py
```

### الطريقة الثانية:
```bash
python init_data.py  # إعداد قاعدة البيانات
python app.py        # تشغيل النظام
```

## 🔐 تسجيل الدخول

بعد تشغيل النظام، افتح المتصفح وانتقل إلى: `http://localhost:5000`

### المستخدمين الافتراضيين:

| اسم المستخدم | كلمة المرور | الدور | الصلاحيات |
|---------------|-------------|-------|-----------|
| `admin` | `admin123` | مدير النظام | جميع الصلاحيات |
| `manager` | `manager123` | مدير الأرشيف | إدارة الكتب والتقارير |
| `user` | `user123` | موظف عادي | إضافة وتعديل الكتب |

## 📋 الاستخدام الأساسي

### 1. إضافة كتاب جديد
1. سجل الدخول بأي حساب له صلاحية الإضافة
2. من القائمة الجانبية، انقر "إضافة كتاب جديد"
3. املأ البيانات المطلوبة:
   - رقم الكتاب
   - تاريخ الكتاب
   - الموضوع
   - نوع الكتاب (وارد/صادر/داخلي)
   - الجهة المرسلة أو المستقبلة
   - درجة الأولوية والسرية
4. أرفق الملفات إن وجدت
5. انقر "حفظ الكتاب"

### 2. البحث في الكتب
1. انتقل إلى "البحث المتقدم"
2. استخدم معايير البحث:
   - نص البحث (في الموضوع والمحتوى)
   - رقم الكتاب
   - نطاق التاريخ
   - نوع الكتاب
   - القسم
   - درجة السرية والأولوية
3. انقر "بحث"

### 3. تحويل الكتب
1. افتح الكتاب المراد تحويله
2. انقر زر "تحويل"
3. اختر المستخدم المراد التحويل إليه
4. حدد نوع التحويل (تحويل/نسخة/إرجاع)
5. أضف ملاحظات إن أردت
6. انقر "تحويل"

### 4. عرض التقارير
1. انتقل إلى "التقارير" (للمديرين فقط)
2. اختر نوع التقرير:
   - ملخص الكتب
   - الكتب حسب النوع
   - الكتب حسب القسم
   - نشاط المستخدمين
3. حدد الفترة الزمنية
4. انقر "إنشاء التقرير"
5. يمكن تصدير التقرير بصيغة PDF أو Excel

## ⚙️ إعدادات النظام

### إضافة أقسام جديدة (للمديرين فقط):
1. انتقل إلى "إعدادات النظام"
2. في قسم "إدارة الأقسام"، انقر "إضافة قسم"
3. املأ اسم القسم ورمزه
4. انقر "حفظ"

### إضافة أنواع كتب جديدة:
1. في "إعدادات النظام"
2. في قسم "أنواع الكتب"، انقر "إضافة نوع"
3. املأ اسم النوع ورمزه
4. انقر "حفظ"

### إدارة المستخدمين:
1. انتقل إلى "إدارة المستخدمين"
2. انقر "إضافة مستخدم جديد"
3. املأ البيانات المطلوبة
4. حدد الدور المناسب
5. انقر "إضافة المستخدم"

## 🔧 استكشاف الأخطاء

### المشكلة: لا يعمل النظام
**الحل:**
1. تأكد من تثبيت Python 3.7+
2. ثبت المتطلبات: `pip install flask flask-sqlalchemy flask-login flask-wtf`
3. شغل: `python run.py`

### المشكلة: لا يمكن تسجيل الدخول
**الحل:**
1. تأكد من استخدام المستخدمين الافتراضيين المذكورين أعلاه
2. تأكد من كتابة اسم المستخدم وكلمة المرور بشكل صحيح
3. إذا استمرت المشكلة، احذف ملف `archive.db` وأعد تشغيل النظام

### المشكلة: لا تظهر الملفات المرفوعة
**الحل:**
1. تأكد من وجود مجلد `uploads`
2. تحقق من صلاحيات الكتابة في المجلد
3. تأكد من أن حجم الملف أقل من 16 ميجابايت

## 📁 هيكل الملفات

```
safaa/
├── app.py              # التطبيق الرئيسي
├── run.py              # ملف التشغيل السريع
├── models.py           # نماذج قاعدة البيانات
├── forms.py            # نماذج الويب
├── init_data.py        # البيانات الأولية
├── requirements.txt    # المتطلبات
├── archive.db          # قاعدة البيانات (تُنشأ تلقائياً)
├── uploads/            # مجلد المرفقات
├── templates/          # قوالب HTML
└── static/             # الملفات الثابتة
```

## 🆘 الدعم

للحصول على المساعدة:
1. راجع ملف `README.md` للتفاصيل الكاملة
2. تحقق من سجل الأخطاء في وحدة التحكم
3. تأكد من تثبيت جميع المتطلبات

---

**ملاحظة:** هذا النظام مصمم للبيئة الحكومية العراقية ويدعم اللغة العربية بشكل كامل.
