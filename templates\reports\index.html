{% extends "base.html" %}

{% block title %}التقارير - نظام الأرشفة الإلكترونية{% endblock %}

{% block content %}
<div class="page-header">
    <h1><i class="fas fa-chart-bar me-2"></i>التقارير والإحصائيات</h1>
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ url_for('dashboard') }}">لوحة التحكم</a></li>
            <li class="breadcrumb-item active">التقارير</li>
        </ol>
    </nav>
</div>

<div class="row">
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-cog me-2"></i>
                    إعدادات التقرير
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="/reports/generate">
                    {{ form.hidden_tag() }}
                    
                    <div class="mb-3">
                        {{ form.report_type.label(class="form-label") }}
                        {{ form.report_type(class="form-select") }}
                    </div>
                    
                    <div class="row">
                        <div class="col-6">
                            <div class="mb-3">
                                {{ form.date_from.label(class="form-label") }}
                                {{ form.date_from(class="form-control") }}
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="mb-3">
                                {{ form.date_to.label(class="form-label") }}
                                {{ form.date_to(class="form-control") }}
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        {{ form.department_id.label(class="form-label") }}
                        {{ form.department_id(class="form-select") }}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.document_type.label(class="form-label") }}
                        {{ form.document_type(class="form-select") }}
                    </div>
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-chart-line me-2"></i>
                            إنشاء التقرير
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- تقارير سريعة -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    تقارير سريعة
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="quickReport('today')">
                        <i class="fas fa-calendar-day me-1"></i>
                        تقرير اليوم
                    </button>
                    <button type="button" class="btn btn-outline-success btn-sm" onclick="quickReport('week')">
                        <i class="fas fa-calendar-week me-1"></i>
                        تقرير الأسبوع
                    </button>
                    <button type="button" class="btn btn-outline-info btn-sm" onclick="quickReport('month')">
                        <i class="fas fa-calendar-alt me-1"></i>
                        تقرير الشهر
                    </button>
                    <button type="button" class="btn btn-outline-warning btn-sm" onclick="quickReport('year')">
                        <i class="fas fa-calendar me-1"></i>
                        تقرير السنة
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-8">
        <div class="card">
            <div class="card-body text-center py-5">
                <i class="fas fa-chart-pie fa-4x text-primary mb-3"></i>
                <h4>إنشاء التقارير</h4>
                <p class="text-muted">اختر نوع التقرير والفترة الزمنية من النموذج على اليسار لإنشاء تقرير مفصل.</p>
                
                <div class="row mt-4">
                    <div class="col-md-6">
                        <div class="card border-0 bg-light">
                            <div class="card-body">
                                <i class="fas fa-file-alt fa-2x text-success mb-2"></i>
                                <h6>ملخص الكتب</h6>
                                <small class="text-muted">إحصائيات شاملة عن جميع الكتب</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card border-0 bg-light">
                            <div class="card-body">
                                <i class="fas fa-users fa-2x text-info mb-2"></i>
                                <h6>نشاط المستخدمين</h6>
                                <small class="text-muted">تقرير عن نشاط المستخدمين</small>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row mt-3">
                    <div class="col-md-6">
                        <div class="card border-0 bg-light">
                            <div class="card-body">
                                <i class="fas fa-building fa-2x text-warning mb-2"></i>
                                <h6>الكتب حسب القسم</h6>
                                <small class="text-muted">توزيع الكتب على الأقسام</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card border-0 bg-light">
                            <div class="card-body">
                                <i class="fas fa-tags fa-2x text-danger mb-2"></i>
                                <h6>الكتب حسب النوع</h6>
                                <small class="text-muted">تصنيف الكتب حسب النوع</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function quickReport(period) {
    var today = new Date();
    var dateFrom, dateTo;
    
    switch(period) {
        case 'today':
            dateFrom = dateTo = today.toISOString().split('T')[0];
            break;
        case 'week':
            var weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
            dateFrom = weekAgo.toISOString().split('T')[0];
            dateTo = today.toISOString().split('T')[0];
            break;
        case 'month':
            var monthAgo = new Date(today.getFullYear(), today.getMonth() - 1, today.getDate());
            dateFrom = monthAgo.toISOString().split('T')[0];
            dateTo = today.toISOString().split('T')[0];
            break;
        case 'year':
            var yearAgo = new Date(today.getFullYear() - 1, today.getMonth(), today.getDate());
            dateFrom = yearAgo.toISOString().split('T')[0];
            dateTo = today.toISOString().split('T')[0];
            break;
    }
    
    // تعبئة النموذج
    $('input[name="date_from"]').val(dateFrom);
    $('input[name="date_to"]').val(dateTo);
    $('select[name="report_type"]').val('documents_summary');
    
    // إرسال النموذج
    $('form').submit();
}

$(document).ready(function() {
    // تعيين التواريخ الافتراضية
    var today = new Date();
    var monthAgo = new Date(today.getFullYear(), today.getMonth() - 1, today.getDate());
    
    if (!$('input[name="date_from"]').val()) {
        $('input[name="date_from"]').val(monthAgo.toISOString().split('T')[0]);
    }
    if (!$('input[name="date_to"]').val()) {
        $('input[name="date_to"]').val(today.toISOString().split('T')[0]);
    }
});
</script>
{% endblock %}
