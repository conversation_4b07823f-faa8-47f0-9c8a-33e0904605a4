{% extends "base.html" %}

{% block title %}لوحة التحكم - نظام الأرشفة الإلكترونية{% endblock %}

{% block content %}
<div class="page-header">
    <h1><i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم</h1>
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item active">لوحة التحكم</li>
        </ol>
    </nav>
</div>

<!-- إحصائيات سريعة -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="stats-card">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <div class="stats-number">{{ total_documents }}</div>
                    <div class="stats-label">إجمالي الكتب</div>
                </div>
                <div class="stats-icon">
                    <i class="fas fa-file-alt fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <div class="stats-number">{{ incoming_documents }}</div>
                    <div class="stats-label">كتب واردة</div>
                </div>
                <div class="stats-icon">
                    <i class="fas fa-inbox fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <div class="stats-number">{{ outgoing_documents }}</div>
                    <div class="stats-label">كتب صادرة</div>
                </div>
                <div class="stats-icon">
                    <i class="fas fa-paper-plane fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <div class="stats-number">{{ internal_documents }}</div>
                    <div class="stats-label">كتب داخلية</div>
                </div>
                <div class="stats-icon">
                    <i class="fas fa-building fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- الكتب الحديثة -->
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-clock me-2"></i>
                    الكتب الحديثة
                </h5>
            </div>
            <div class="card-body">
                {% if recent_documents %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>رقم الكتاب</th>
                                <th>الموضوع</th>
                                <th>النوع</th>
                                <th>التاريخ</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for doc in recent_documents %}
                            <tr>
                                <td>
                                    <a href="{{ url_for('view_document', id=doc.id) }}" class="text-decoration-none">
                                        {{ doc.get_full_number() }}
                                    </a>
                                </td>
                                <td>{{ doc.subject[:50] }}{% if doc.subject|length > 50 %}...{% endif %}</td>
                                <td>
                                    {% if doc.document_type == 'incoming' %}
                                        <span class="badge bg-success">وارد</span>
                                    {% elif doc.document_type == 'outgoing' %}
                                        <span class="badge bg-info">صادر</span>
                                    {% else %}
                                        <span class="badge bg-warning">داخلي</span>
                                    {% endif %}
                                </td>
                                <td>{{ doc.document_date.strftime('%Y-%m-%d') }}</td>
                                <td>
                                    {% if doc.priority == 'urgent' %}
                                        <span class="badge bg-danger">عاجل</span>
                                    {% elif doc.priority == 'high' %}
                                        <span class="badge bg-warning">عالي</span>
                                    {% else %}
                                        <span class="badge bg-secondary">عادي</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                    <p class="text-muted">لا توجد كتب حديثة</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- الكتب المحولة إلي -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bell me-2"></i>
                    الكتب المحولة إليك
                </h5>
            </div>
            <div class="card-body">
                {% if my_documents %}
                <div class="list-group list-group-flush">
                    {% for movement in my_documents %}
                    <div class="list-group-item border-0 px-0">
                        <div class="d-flex w-100 justify-content-between">
                            <h6 class="mb-1">
                                <a href="{{ url_for('view_document', id=movement.document.id) }}" class="text-decoration-none">
                                    {{ movement.document.get_full_number() }}
                                </a>
                            </h6>
                            <small>{{ movement.created_at.strftime('%m-%d') }}</small>
                        </div>
                        <p class="mb-1">{{ movement.document.subject[:40] }}{% if movement.document.subject|length > 40 %}...{% endif %}</p>
                        <small class="text-muted">من: {{ movement.from_user }}</small>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                    <p class="text-muted">لا توجد كتب جديدة</p>
                </div>
                {% endif %}
            </div>
        </div>
        
        <!-- روابط سريعة -->
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-link me-2"></i>
                    روابط سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    {% if current_user.has_permission('create') %}
                    <a href="{{ url_for('add_document') }}" class="btn btn-outline-primary">
                        <i class="fas fa-plus me-2"></i>
                        إضافة كتاب جديد
                    </a>
                    {% endif %}
                    
                    <a href="{{ url_for('search') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-search me-2"></i>
                        البحث المتقدم
                    </a>
                    
                    {% if current_user.has_permission('view_reports') %}
                    <a href="{{ url_for('reports') }}" class="btn btn-outline-info">
                        <i class="fas fa-chart-bar me-2"></i>
                        التقارير
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // تحديث الإحصائيات كل 30 ثانية
    setInterval(function() {
        // يمكن إضافة AJAX لتحديث الإحصائيات
    }, 30000);
});
</script>
{% endblock %}
