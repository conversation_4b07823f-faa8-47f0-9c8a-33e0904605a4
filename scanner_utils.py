"""
وحدة التعامل مع السكانر
"""

import os
import sys
import tempfile
from datetime import datetime
from PIL import Image
import io
import base64

class ScannerManager:
    """مدير السكانر"""
    
    def __init__(self):
        self.available_scanners = []
        self.current_scanner = None
        self.scan_settings = {
            'resolution': 300,  # DPI
            'mode': 'color',    # color, gray, lineart
            'format': 'JPEG',   # JPEG, PNG, PDF
            'brightness': 0,
            'contrast': 0
        }
    
    def detect_scanners(self):
        """اكتشاف السكانرات المتاحة"""
        try:
            # محاولة استخدام SANE (Linux/Mac)
            if sys.platform in ['linux', 'darwin']:
                try:
                    import sane
                    sane.init()
                    devices = sane.get_devices()
                    self.available_scanners = [
                        {
                            'id': i,
                            'name': device[1],
                            'vendor': device[0],
                            'model': device[2],
                            'type': device[3]
                        }
                        for i, device in enumerate(devices)
                    ]
                    return True
                except ImportError:
                    pass
            
            # محاولة استخدام WIA (Windows)
            elif sys.platform == 'win32':
                try:
                    import win32com.client
                    wia = win32com.client.Dispatch("WIA.DeviceManager")
                    devices = wia.DeviceInfos
                    
                    self.available_scanners = []
                    for i in range(devices.Count):
                        device = devices.Item(i + 1)
                        if device.Type == 1:  # Scanner
                            self.available_scanners.append({
                                'id': device.DeviceID,
                                'name': device.Properties("Name").Value,
                                'vendor': device.Properties("Manufacturer").Value if "Manufacturer" in [p.Name for p in device.Properties] else "Unknown",
                                'model': device.Properties("Name").Value,
                                'type': 'Scanner'
                            })
                    return len(self.available_scanners) > 0
                except ImportError:
                    pass
            
            # إذا لم تنجح الطرق السابقة، استخدم طريقة بديلة
            return self._detect_scanners_fallback()
            
        except Exception as e:
            print(f"خطأ في اكتشاف السكانرات: {e}")
            return False
    
    def _detect_scanners_fallback(self):
        """طريقة بديلة لاكتشاف السكانرات"""
        # محاولة استخدام أوامر النظام
        if sys.platform == 'win32':
            # Windows - استخدام PowerShell
            try:
                import subprocess
                result = subprocess.run([
                    'powershell', '-Command',
                    'Get-WmiObject -Class Win32_PnPEntity | Where-Object {$_.Name -like "*scan*" -or $_.Name -like "*imaging*"} | Select-Object Name, Manufacturer'
                ], capture_output=True, text=True, timeout=10)
                
                if result.returncode == 0 and result.stdout.strip():
                    # إضافة سكانر افتراضي إذا وُجدت أجهزة
                    self.available_scanners = [{
                        'id': 'default',
                        'name': 'سكانر افتراضي',
                        'vendor': 'Windows',
                        'model': 'WIA Scanner',
                        'type': 'Scanner'
                    }]
                    return True
            except:
                pass
        
        # إضافة سكانر وهمي للاختبار
        self.available_scanners = [{
            'id': 'demo',
            'name': 'سكانر تجريبي',
            'vendor': 'Demo',
            'model': 'Virtual Scanner',
            'type': 'Scanner'
        }]
        return True
    
    def scan_document(self, scanner_id=None, settings=None):
        """مسح مستند"""
        try:
            if settings:
                self.scan_settings.update(settings)
            
            if not self.available_scanners:
                self.detect_scanners()
            
            if not self.available_scanners:
                raise Exception("لا توجد سكانرات متاحة")
            
            # اختيار السكانر
            if scanner_id is None:
                scanner_id = self.available_scanners[0]['id']
            
            # تنفيذ المسح حسب النظام
            if sys.platform == 'win32':
                return self._scan_windows(scanner_id)
            elif sys.platform in ['linux', 'darwin']:
                return self._scan_linux_mac(scanner_id)
            else:
                return self._scan_demo()
                
        except Exception as e:
            raise Exception(f"خطأ في المسح: {str(e)}")
    
    def _scan_windows(self, scanner_id):
        """مسح باستخدام Windows WIA"""
        try:
            import win32com.client
            
            # إنشاء كائن WIA
            wia = win32com.client.Dispatch("WIA.CommonDialog")
            
            # عرض نافذة المسح
            image = wia.ShowAcquireImage()
            
            if image:
                # حفظ الصورة مؤقتاً
                temp_file = tempfile.NamedTemporaryFile(suffix='.jpg', delete=False)
                image.SaveFile(temp_file.name)
                
                # قراءة الصورة وتحويلها إلى base64
                with open(temp_file.name, 'rb') as f:
                    image_data = f.read()
                
                # حذف الملف المؤقت
                os.unlink(temp_file.name)
                
                return {
                    'success': True,
                    'image_data': base64.b64encode(image_data).decode('utf-8'),
                    'format': 'JPEG',
                    'size': len(image_data)
                }
            else:
                raise Exception("تم إلغاء المسح")
                
        except ImportError:
            return self._scan_demo()
        except Exception as e:
            raise Exception(f"خطأ في مسح Windows: {str(e)}")
    
    def _scan_linux_mac(self, scanner_id):
        """مسح باستخدام SANE (Linux/Mac)"""
        try:
            import sane
            
            # فتح السكانر
            scanner = sane.open(self.available_scanners[0]['name'])
            
            # تطبيق الإعدادات
            if hasattr(scanner, 'resolution'):
                scanner.resolution = self.scan_settings['resolution']
            if hasattr(scanner, 'mode'):
                scanner.mode = self.scan_settings['mode']
            
            # تنفيذ المسح
            scanner.start()
            image = scanner.snap()
            scanner.close()
            
            # تحويل الصورة إلى PIL Image
            pil_image = Image.fromarray(image)
            
            # حفظ في buffer
            buffer = io.BytesIO()
            pil_image.save(buffer, format=self.scan_settings['format'])
            image_data = buffer.getvalue()
            
            return {
                'success': True,
                'image_data': base64.b64encode(image_data).decode('utf-8'),
                'format': self.scan_settings['format'],
                'size': len(image_data)
            }
            
        except ImportError:
            return self._scan_demo()
        except Exception as e:
            raise Exception(f"خطأ في مسح SANE: {str(e)}")
    
    def _scan_demo(self):
        """مسح تجريبي (إنشاء صورة وهمية)"""
        try:
            # إنشاء صورة تجريبية
            from PIL import Image, ImageDraw, ImageFont
            
            # إنشاء صورة بيضاء
            width, height = 800, 1000
            image = Image.new('RGB', (width, height), 'white')
            draw = ImageDraw.Draw(image)
            
            # إضافة نص تجريبي
            try:
                # محاولة استخدام خط عربي
                font = ImageFont.truetype("arial.ttf", 40)
            except:
                font = ImageFont.load_default()
            
            # رسم إطار
            draw.rectangle([50, 50, width-50, height-50], outline='black', width=3)
            
            # إضافة نص
            text_lines = [
                "مستند تجريبي",
                "تم إنشاؤه بواسطة السكانر التجريبي",
                f"التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M')}",
                "",
                "هذا مستند تجريبي لاختبار",
                "وظيفة المسح الضوئي",
                "",
                "يمكن استبداله بمسح حقيقي",
                "عند توصيل سكانر فعلي"
            ]
            
            y_position = 100
            for line in text_lines:
                bbox = draw.textbbox((0, 0), line, font=font)
                text_width = bbox[2] - bbox[0]
                x_position = (width - text_width) // 2
                draw.text((x_position, y_position), line, fill='black', font=font)
                y_position += 60
            
            # حفظ في buffer
            buffer = io.BytesIO()
            image.save(buffer, format='JPEG', quality=85)
            image_data = buffer.getvalue()
            
            return {
                'success': True,
                'image_data': base64.b64encode(image_data).decode('utf-8'),
                'format': 'JPEG',
                'size': len(image_data),
                'demo': True
            }
            
        except Exception as e:
            raise Exception(f"خطأ في المسح التجريبي: {str(e)}")
    
    def save_scanned_image(self, image_data, filename, upload_folder):
        """حفظ الصورة الممسوحة"""
        try:
            # فك تشفير base64
            image_bytes = base64.b64decode(image_data)
            
            # إنشاء اسم ملف فريد
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"scan_{timestamp}_{filename}"
            file_path = os.path.join(upload_folder, filename)
            
            # حفظ الملف
            with open(file_path, 'wb') as f:
                f.write(image_bytes)
            
            return {
                'success': True,
                'filename': filename,
                'file_path': file_path,
                'size': len(image_bytes)
            }
            
        except Exception as e:
            raise Exception(f"خطأ في حفظ الصورة: {str(e)}")

# إنشاء مثيل عام
scanner_manager = ScannerManager()
