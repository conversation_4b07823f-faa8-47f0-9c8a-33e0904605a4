{% extends "base.html" %}

{% block title %}قائمة الكتب - نظام الأرشفة الإلكترونية{% endblock %}

{% block extra_css %}
<style>
/* نافذة معاينة الصورة */
.image-preview-tooltip {
    position: fixed;
    z-index: 9999;
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    max-width: 350px;
    max-height: 450px;
    pointer-events: auto;
}

.preview-content {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    border-bottom: 1px solid #eee;
    background-color: #f8f9fa;
    border-radius: 8px 8px 0 0;
}

.preview-title {
    font-weight: 600;
    font-size: 14px;
    color: #333;
}

.btn-close-preview {
    background: none;
    border: none;
    font-size: 18px;
    color: #666;
    cursor: pointer;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-close-preview:hover {
    color: #000;
}

.preview-body {
    padding: 15px;
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 200px;
}

.preview-image {
    max-width: 100%;
    max-height: 300px;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    cursor: pointer;
}

.preview-info {
    margin-top: 10px;
    text-align: center;
}

.document-preview-btn {
    position: relative;
}
</style>
{% endblock %}

{% block content %}
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1><i class="fas fa-file-alt me-2"></i>قائمة الكتب</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('dashboard') }}">لوحة التحكم</a></li>
                    <li class="breadcrumb-item active">الكتب</li>
                </ol>
            </nav>
        </div>
        {% if current_user.has_permission('create') %}
        <a href="{{ url_for('add_document') }}" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>
            إضافة كتاب جديد
        </a>
        {% endif %}
    </div>
</div>

<div class="card">
    <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
                <i class="fas fa-list me-2"></i>
                جميع الكتب ({{ documents.total }} كتاب)
            </h5>
            <div class="btn-group" role="group">
                <a href="{{ url_for('search') }}" class="btn btn-outline-secondary btn-sm">
                    <i class="fas fa-search me-1"></i>
                    بحث متقدم
                </a>
                <button type="button" class="btn btn-outline-secondary btn-sm" onclick="window.print()">
                    <i class="fas fa-print me-1"></i>
                    طباعة
                </button>
                {% if current_user.has_permission('delete') %}
                <button type="button" class="btn btn-outline-danger btn-sm" id="deleteSelectedBtn" onclick="deleteSelected()" style="display: none;">
                    <i class="fas fa-trash me-1"></i>
                    حذف المحدد (<span id="selectedCount">0</span>)
                </button>
                {% endif %}
            </div>
        </div>
    </div>
    <div class="card-body">
        {% if documents.items %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-light">
                    <tr>
                        {% if current_user.has_permission('delete') %}
                        <th width="40">
                            <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                        </th>
                        {% endif %}
                        <th>رقم الكتاب</th>
                        <th>الموضوع</th>
                        <th>النوع</th>
                        <th>الجهة</th>
                        <th>التاريخ</th>
                        <th>الأولوية</th>
                        <th>المرفقات</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for doc in documents.items %}
                    <tr>
                        {% if current_user.has_permission('delete') %}
                        <td>
                            <input type="checkbox" class="document-checkbox" value="{{ doc.id }}" onchange="updateSelectedCount()">
                        </td>
                        {% endif %}
                        <td>
                            <a href="{{ url_for('view_document', id=doc.id) }}" class="text-decoration-none fw-bold">
                                {{ doc.get_full_number() }}
                            </a>
                        </td>
                        <td>
                            <div class="text-truncate" style="max-width: 200px;" title="{{ doc.subject }}">
                                {{ doc.subject }}
                            </div>
                        </td>
                        <td>
                            {% if doc.document_type == 'incoming' %}
                                <span class="badge bg-success">
                                    <i class="fas fa-inbox me-1"></i>وارد
                                </span>
                            {% elif doc.document_type == 'outgoing' %}
                                <span class="badge bg-info">
                                    <i class="fas fa-paper-plane me-1"></i>صادر
                                </span>
                            {% else %}
                                <span class="badge bg-warning">
                                    <i class="fas fa-building me-1"></i>داخلي
                                </span>
                            {% endif %}
                        </td>
                        <td>
                            <small class="text-muted">
                                {% if doc.document_type == 'incoming' and doc.sender %}
                                    من: {{ doc.sender[:20] }}{% if doc.sender|length > 20 %}...{% endif %}
                                {% elif doc.document_type == 'outgoing' and doc.receiver %}
                                    إلى: {{ doc.receiver[:20] }}{% if doc.receiver|length > 20 %}...{% endif %}
                                {% else %}
                                    -
                                {% endif %}
                            </small>
                        </td>
                        <td>
                            <small>{{ doc.document_date.strftime('%Y-%m-%d') }}</small>
                        </td>
                        <td>
                            {% if doc.priority == 'urgent' %}
                                <span class="badge bg-danger">عاجل</span>
                            {% elif doc.priority == 'high' %}
                                <span class="badge bg-warning">عالي</span>
                            {% elif doc.priority == 'low' %}
                                <span class="badge bg-secondary">منخفض</span>
                            {% else %}
                                <span class="badge bg-light text-dark">عادي</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if doc.has_attachments %}
                                <i class="fas fa-paperclip text-success" title="{{ doc.attachments_count }} مرفق"></i>
                                <small class="text-muted">{{ doc.attachments_count }}</small>
                            {% else %}
                                <i class="fas fa-minus text-muted"></i>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <a href="{{ url_for('view_document', id=doc.id) }}"
                                   class="btn btn-outline-primary document-preview-btn"
                                   title="عرض"
                                   data-document-id="{{ doc.id }}"
                                   data-bs-toggle="tooltip"
                                   data-bs-placement="top">
                                    <i class="fas fa-eye"></i>
                                </a>
                                {% if current_user.has_permission('update') %}
                                <a href="{{ url_for('edit_document', id=doc.id) }}" class="btn btn-outline-secondary" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                {% endif %}
                                {% if current_user.has_permission('create') %}
                                <button type="button" class="btn btn-outline-info" title="تحويل" onclick="transferDocument({{ doc.id }})">
                                    <i class="fas fa-share"></i>
                                </button>
                                {% endif %}
                                {% if current_user.has_permission('delete') %}
                                <button type="button" class="btn btn-outline-danger" title="حذف" onclick="deleteDocument({{ doc.id }})">
                                    <i class="fas fa-trash"></i>
                                </button>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        {% if documents.pages > 1 %}
        <nav aria-label="تصفح الصفحات">
            <ul class="pagination justify-content-center">
                {% if documents.has_prev %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('documents', page=documents.prev_num) }}">السابق</a>
                </li>
                {% endif %}
                
                {% for page_num in documents.iter_pages() %}
                    {% if page_num %}
                        {% if page_num != documents.page %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('documents', page=page_num) }}">{{ page_num }}</a>
                        </li>
                        {% else %}
                        <li class="page-item active">
                            <span class="page-link">{{ page_num }}</span>
                        </li>
                        {% endif %}
                    {% else %}
                    <li class="page-item disabled">
                        <span class="page-link">…</span>
                    </li>
                    {% endif %}
                {% endfor %}
                
                {% if documents.has_next %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('documents', page=documents.next_num) }}">التالي</a>
                </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}
        
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-inbox fa-4x text-muted mb-3"></i>
            <h4 class="text-muted">لا توجد كتب</h4>
            <p class="text-muted">لم يتم إضافة أي كتب بعد.</p>
            {% if current_user.has_permission('create') %}
            <a href="{{ url_for('add_document') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                إضافة أول كتاب
            </a>
            {% endif %}
        </div>
        {% endif %}
    </div>
</div>

<!-- Modal تحويل الكتاب -->
<div class="modal fade" id="transferModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تحويل الكتاب</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="transferForm">
                    <input type="hidden" id="documentId" name="document_id">
                    <div class="mb-3">
                        <label for="toUser" class="form-label">تحويل إلى</label>
                        <select class="form-select" id="toUser" name="to_user_id" required>
                            <option value="">اختر المستخدم...</option>
                            <!-- سيتم ملؤها بـ JavaScript -->
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="movementType" class="form-label">نوع التحويل</label>
                        <select class="form-select" id="movementType" name="movement_type" required>
                            <option value="forward">تحويل</option>
                            <option value="copy">نسخة</option>
                            <option value="return">إرجاع</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="notes" class="form-label">ملاحظات</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="submitTransfer()">تحويل</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal تأكيد الحذف -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>تحذير!</strong> هذا الإجراء لا يمكن التراجع عنه.
                </div>
                <p id="deleteMessage">هل أنت متأكد من حذف هذا الكتاب؟</p>
                <div id="deleteDetails"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">
                    <i class="fas fa-trash me-2"></i>حذف
                </button>
            </div>
        </div>
    </div>
</div>

<!-- نافذة معاينة الصورة -->
<div id="imagePreviewTooltip" class="image-preview-tooltip" style="display: none;">
    <div class="preview-content">
        <div class="preview-header">
            <span class="preview-title">معاينة الكتاب</span>
            <button type="button" class="btn-close-preview" onclick="hideImagePreview()">×</button>
        </div>
        <div class="preview-body">
            <div id="previewLoader" class="text-center">
                <i class="fas fa-spinner fa-spin"></i>
                <div>جاري التحميل...</div>
            </div>
            <div id="previewContent" style="display: none;">
                <img id="previewImage" src="" alt="معاينة الكتاب" class="preview-image">
                <div id="previewInfo" class="preview-info">
                    <small class="text-muted">اضغط للعرض الكامل</small>
                </div>
            </div>
            <div id="previewError" style="display: none;" class="text-center text-muted">
                <i class="fas fa-image"></i>
                <div>لا توجد صورة متاحة</div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function transferDocument(documentId) {
    $('#documentId').val(documentId);
    $('#transferModal').modal('show');
    
    // تحميل قائمة المستخدمين
    $.get('/api/users', function(users) {
        var select = $('#toUser');
        select.empty().append('<option value="">اختر المستخدم...</option>');
        users.forEach(function(user) {
            select.append('<option value="' + user.id + '">' + user.full_name + ' - ' + user.department + '</option>');
        });
    });
}

function submitTransfer() {
    var formData = {
        document_id: $('#documentId').val(),
        to_user_id: $('#toUser').val(),
        movement_type: $('#movementType').val(),
        notes: $('#notes').val()
    };
    
    $.post('/api/transfer-document', formData)
        .done(function(response) {
            $('#transferModal').modal('hide');
            location.reload();
        })
        .fail(function(xhr) {
            alert('حدث خطأ أثناء تحويل الكتاب: ' + xhr.responseJSON.message);
        });
}

// وظائف الحذف
function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.document-checkbox');

    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });

    updateSelectedCount();
}

function updateSelectedCount() {
    const checkboxes = document.querySelectorAll('.document-checkbox:checked');
    const count = checkboxes.length;
    const deleteBtn = document.getElementById('deleteSelectedBtn');
    const countSpan = document.getElementById('selectedCount');

    countSpan.textContent = count;

    if (count > 0) {
        deleteBtn.style.display = 'inline-block';
    } else {
        deleteBtn.style.display = 'none';
    }

    // تحديث حالة "تحديد الكل"
    const allCheckboxes = document.querySelectorAll('.document-checkbox');
    const selectAll = document.getElementById('selectAll');

    if (count === 0) {
        selectAll.indeterminate = false;
        selectAll.checked = false;
    } else if (count === allCheckboxes.length) {
        selectAll.indeterminate = false;
        selectAll.checked = true;
    } else {
        selectAll.indeterminate = true;
    }
}

function deleteDocument(documentId) {
    console.log('deleteDocument called with ID:', documentId);
    // حذف كتاب واحد
    $('#deleteMessage').text('هل أنت متأكد من حذف هذا الكتاب؟');
    $('#deleteDetails').html('<small class="text-muted">رقم الكتاب: ' + documentId + '</small>');

    $('#confirmDeleteBtn').off('click').on('click', function() {
        console.log('Confirm delete clicked for:', documentId);
        performDelete([documentId]);
    });

    $('#deleteModal').modal('show');
}

function deleteSelected() {
    // حذف الكتب المحددة
    const checkboxes = document.querySelectorAll('.document-checkbox:checked');
    const documentIds = Array.from(checkboxes).map(cb => cb.value);

    if (documentIds.length === 0) {
        alert('يرجى تحديد كتاب واحد على الأقل للحذف');
        return;
    }

    $('#deleteMessage').text(`هل أنت متأكد من حذف ${documentIds.length} كتاب؟`);
    $('#deleteDetails').html('<small class="text-muted">سيتم حذف جميع الكتب المحددة نهائياً</small>');

    $('#confirmDeleteBtn').off('click').on('click', function() {
        performDelete(documentIds);
    });

    $('#deleteModal').modal('show');
}

function performDelete(documentIds) {
    console.log('performDelete called with IDs:', documentIds);
    const deleteBtn = $('#confirmDeleteBtn');
    const originalText = deleteBtn.html();

    // تعطيل الزر وإظهار تحميل
    deleteBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-2"></i>جاري الحذف...');

    console.log('Sending AJAX request to:', '/api/documents/delete');
    console.log('CSRF token:', $('meta[name=csrf-token]').attr('content'));

    // إعداد البيانات بطريقة صحيحة
    var formData = new FormData();
    documentIds.forEach(function(id) {
        formData.append('document_ids', id);
    });
    formData.append('csrf_token', $('meta[name=csrf-token]').attr('content'));

    $.ajax({
        url: '/api/documents/delete',
        method: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
            $('#deleteModal').modal('hide');

            // إظهار رسالة نجاح
            showAlert('success', `تم حذف ${documentIds.length} كتاب بنجاح`);

            // إعادة تحميل الصفحة بعد ثانيتين
            setTimeout(() => {
                location.reload();
            }, 2000);
        },
        error: function(xhr) {
            let message = 'حدث خطأ أثناء الحذف';
            if (xhr.responseJSON && xhr.responseJSON.message) {
                message = xhr.responseJSON.message;
            }
            showAlert('danger', message);
        },
        complete: function() {
            // إعادة تفعيل الزر
            deleteBtn.prop('disabled', false).html(originalText);
        }
    });
}

function showAlert(type, message) {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-triangle'} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    // إضافة التنبيه في أعلى الصفحة
    $('.page-header').after(alertHtml);
}

// معاينة الصورة
let previewTimeout;
let currentPreviewBtn = null;

$(document).ready(function() {
    // إضافة أحداث المعاينة
    $('.document-preview-btn').on('mouseenter', function(e) {
        const documentId = $(this).data('document-id');
        currentPreviewBtn = this;

        // تأخير قصير قبل إظهار المعاينة
        previewTimeout = setTimeout(() => {
            showImagePreview(documentId, e);
        }, 500);
    });

    $('.document-preview-btn').on('mouseleave', function() {
        clearTimeout(previewTimeout);
        // تأخير قبل إخفاء المعاينة للسماح بالانتقال إليها
        setTimeout(() => {
            if (!$('#imagePreviewTooltip:hover').length) {
                hideImagePreview();
            }
        }, 200);
    });

    // إخفاء المعاينة عند مغادرة النافذة
    $('#imagePreviewTooltip').on('mouseleave', function() {
        hideImagePreview();
    });
});

function showImagePreview(documentId, event) {
    const tooltip = $('#imagePreviewTooltip');
    const loader = $('#previewLoader');
    const content = $('#previewContent');
    const error = $('#previewError');

    // إعادة تعيين الحالة
    loader.show();
    content.hide();
    error.hide();

    // تحديد موقع النافذة
    const rect = currentPreviewBtn.getBoundingClientRect();
    const tooltipWidth = 350;
    const tooltipHeight = 450;

    let left = rect.right + 10;
    let top = rect.top - (tooltipHeight / 2) + (rect.height / 2);

    // التأكد من عدم خروج النافذة من الشاشة
    if (left + tooltipWidth > window.innerWidth) {
        left = rect.left - tooltipWidth - 10;
    }
    if (top < 10) {
        top = 10;
    }
    if (top + tooltipHeight > window.innerHeight) {
        top = window.innerHeight - tooltipHeight - 10;
    }

    tooltip.css({
        left: left + 'px',
        top: top + 'px',
        display: 'block'
    });

    // جلب صورة المعاينة
    $.ajax({
        url: `/api/documents/${documentId}/preview`,
        method: 'GET',
        success: function(response) {
            if (response.success && response.image_url) {
                $('#previewImage').attr('src', response.image_url);
                $('#previewImage').off('click').on('click', function() {
                    window.open(`/documents/${documentId}`, '_blank');
                });
                loader.hide();
                content.show();
            } else {
                showPreviewError();
            }
        },
        error: function() {
            showPreviewError();
        }
    });
}

function showPreviewError() {
    $('#previewLoader').hide();
    $('#previewContent').hide();
    $('#previewError').show();
}

function hideImagePreview() {
    $('#imagePreviewTooltip').hide();
    clearTimeout(previewTimeout);
}

// تحديث الصفحة كل 30 ثانية للحصول على آخر التحديثات
setInterval(function() {
    // يمكن إضافة تحديث تلقائي هنا
}, 30000);
</script>
{% endblock %}
