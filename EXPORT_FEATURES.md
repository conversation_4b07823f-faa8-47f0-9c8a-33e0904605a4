# وظائف تصدير التقارير

تم إضافة وظائف تصدير التقارير إلى ملفات Excel و PDF في نظام الأرشفة الإلكترونية.

## الميزات المضافة

### 1. تصدير إلى Excel (.xlsx)
- تصدير جميع أنواع التقارير إلى ملفات Excel
- تنسيق احترافي مع ألوان وخطوط مناسبة
- معلومات التقرير (التاريخ، النوع، إلخ)
- جداول مفصلة للبيانات
- تنسيق تلقائي لعرض الأعمدة

### 2. تصدير إلى PDF
- تصدير التقارير إلى ملفات PDF
- دعم النصوص العربية
- تخطيط احترافي مع جداول منسقة
- معلومات التقرير والإحصائيات
- تحسين للطباعة

## كيفية الاستخدام

1. انتقل إلى صفحة التقارير
2. اختر نوع التقرير والفترة الزمنية
3. انقر على "إنشاء التقرير"
4. في صفحة النتائج، انقر على:
   - "تصدير Excel" لتحميل ملف .xlsx
   - "تصدير PDF" لتحميل ملف .pdf

## أنواع التقارير المدعومة

### 1. ملخص الكتب (documents_summary)
- إحصائيات عامة (إجمالي، وارد، صادر، داخلي)
- تفاصيل الكتب مع جميع المعلومات

### 2. الكتب حسب النوع (documents_by_type)
- توزيع الكتب حسب النوع (وارد/صادر/داخلي)
- النسب المئوية لكل نوع

### 3. الكتب حسب القسم (documents_by_department)
- توزيع الكتب حسب الأقسام
- إحصائيات مفصلة لكل قسم

### 4. نشاط المستخدمين (user_activity)
- عدد الكتب المنشأة لكل مستخدم
- إحصائيات الأداء

## التقنيات المستخدمة

### Excel Export
- مكتبة `openpyxl` لإنشاء ملفات Excel
- تنسيق الخلايا والألوان
- تعديل عرض الأعمدة تلقائياً

### PDF Export
- مكتبة `reportlab` لإنشاء ملفات PDF
- دعم الجداول والتنسيق
- تخطيط مناسب للطباعة

## الملفات المعدلة

### Backend (app.py)
- إضافة مسار `/reports/export/excel`
- إضافة مسار `/reports/export/pdf`
- دوال معالجة التصدير

### Frontend (templates/reports/result.html)
- أزرار التصدير في واجهة النتائج
- JavaScript لإرسال طلبات التصدير

### Base Template (templates/base.html)
- إضافة CSRF token للأمان

## الأمان

- استخدام CSRF tokens لحماية طلبات التصدير
- التحقق من صلاحيات المستخدم
- التحقق من صحة البيانات المرسلة

## أسماء الملفات

الملفات المصدرة تحمل أسماء وصفية:
```
report_{نوع_التقرير}_{تاريخ_البداية}_{تاريخ_النهاية}.{امتداد}
```

مثال:
```
report_documents_summary_20241201_20241231.xlsx
report_user_activity_20241201_20241231.pdf
```

## المتطلبات

تأكد من تثبيت المكتبات المطلوبة:
```bash
pip install openpyxl reportlab
```

## الاستكشاف والإصلاح

### مشاكل شائعة:
1. **خطأ في CSRF Token**: تأكد من وجود meta tag في base.html
2. **خطأ في المكتبات**: تأكد من تثبيت openpyxl و reportlab
3. **مشاكل في التنسيق**: تحقق من إعدادات اللغة والترميز

### سجلات الأخطاء:
راجع سجلات Flask للحصول على تفاصيل الأخطاء في حالة فشل التصدير.
